using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Management;
using System.Threading.Tasks;

namespace SystemReinstaller.Utils
{
    /// <summary>
    /// 磁盘工具类，提供磁盘操作和系统镜像处理功能
    /// </summary>
    public static class DiskUtils
    {
        /// <summary>
        /// 获取系统磁盘信息
        /// </summary>
        /// <returns>磁盘信息列表</returns>
        public static List<DiskInfo> GetDisks()
        {
            var disks = new List<DiskInfo>();
            
            try
            {
                // 使用WMI获取磁盘信息
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive"))
                {
                    foreach (var disk in searcher.Get())
                    {
                        var diskInfo = new DiskInfo
                        {
                            DeviceID = disk["DeviceID"].ToString(),
                            Model = disk["Model"].ToString(),
                            Size = Convert.ToUInt64(disk["Size"]),
                            Caption = disk["Caption"].ToString()
                        };
                        
                        disks.Add(diskInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取磁盘信息失败: {ex.Message}");
            }
            
            return disks;
        }
        
        /// <summary>
        /// 获取分区信息
        /// </summary>
        /// <returns>分区信息列表</returns>
        public static List<PartitionInfo> GetPartitions()
        {
            var partitions = new List<PartitionInfo>();
            
            try
            {
                // 获取逻辑驱动器信息
                foreach (var drive in DriveInfo.GetDrives())
                {
                    if (drive.IsReady)
                    {
                        var partition = new PartitionInfo
                        {
                            DriveLetter = drive.Name,
                            VolumeLabel = drive.VolumeLabel,
                            DriveType = drive.DriveType,
                            TotalSize = drive.TotalSize,
                            FreeSpace = drive.AvailableFreeSpace,
                            DriveFormat = drive.DriveFormat
                        };
                        
                        partitions.Add(partition);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取分区信息失败: {ex.Message}");
            }
            
            return partitions;
        }
        
        /// <summary>
        /// 验证系统镜像文件
        /// </summary>
        /// <param name="imagePath">镜像文件路径</param>
        /// <returns>验证结果</returns>
        public static bool VerifySystemImage(string imagePath)
        {
            // 这里只是简单验证文件是否存在及扩展名是否为iso
            if (!File.Exists(imagePath))
            {
                return false;
            }
            
            string extension = Path.GetExtension(imagePath).ToLower();
            return extension == ".iso";
        }
        
        /// <summary>
        /// 模拟备份系统文件
        /// </summary>
        /// <param name="backupPath">备份路径</param>
        /// <returns>备份结果</returns>
        public static async Task<bool> BackupSystemFiles(string backupPath)
        {
            // 在实际应用中，应该实现真正的系统文件备份逻辑
            // 这里仅作演示
            
            try
            {
                // 创建备份目录
                if (!Directory.Exists(backupPath))
                {
                    Directory.CreateDirectory(backupPath);
                }
                
                // 模拟备份过程
                await Task.Delay(2000);
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"备份系统文件失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 模拟格式化磁盘
        /// </summary>
        /// <param name="driveLetter">驱动器盘符</param>
        /// <returns>格式化结果</returns>
        public static bool FormatDisk(string driveLetter)
        {
            // 在实际应用中，应该调用Windows API进行磁盘格式化
            // 这里仅作演示，不执行实际格式化操作
            
            try
            {
                // 模拟格式化过程
                Debug.WriteLine($"模拟格式化磁盘: {driveLetter}");
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"格式化磁盘失败: {ex.Message}");
                return false;
            }
        }
    }
    
    /// <summary>
    /// 磁盘信息类
    /// </summary>
    public class DiskInfo
    {
        public string DeviceID { get; set; }
        public string Model { get; set; }
        public ulong Size { get; set; }
        public string Caption { get; set; }
        
        public string SizeGB => (Size / 1024.0 / 1024.0 / 1024.0).ToString("F2") + " GB";
        
        public override string ToString()
        {
            return $"{Model} ({SizeGB})";
        }
    }
    
    /// <summary>
    /// 分区信息类
    /// </summary>
    public class PartitionInfo
    {
        public string DriveLetter { get; set; }
        public string VolumeLabel { get; set; }
        public DriveType DriveType { get; set; }
        public long TotalSize { get; set; }
        public long FreeSpace { get; set; }
        public string DriveFormat { get; set; }
        
        public string TotalSizeGB => (TotalSize / 1024.0 / 1024.0 / 1024.0).ToString("F2") + " GB";
        public string FreeSpaceGB => (FreeSpace / 1024.0 / 1024.0 / 1024.0).ToString("F2") + " GB";
        
        public override string ToString()
        {
            return $"{DriveLetter} {VolumeLabel} ({FreeSpaceGB} free of {TotalSizeGB})";
        }
    }
} 