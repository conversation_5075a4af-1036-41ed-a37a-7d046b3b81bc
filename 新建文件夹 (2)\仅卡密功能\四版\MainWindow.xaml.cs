﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using SystemReinstaller.Utils;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Threading;
using System.Net.Http;

// 明确指定需要使用的类型
using WinForms = System.Windows.Forms;
using WPF = System.Windows;

namespace SystemReinstaller;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    // 模拟下载进度的计时器
    private BackgroundWorker downloadWorker = null!;
    private BackgroundWorker localInstallWorker = null!;
    private string websiteUrl = "https://windows.com";
    private string selectedImagePath = string.Empty;
    private string backupPath = string.Empty;
    private string localBackupPath = string.Empty;
    private long totalDownloadSize = 0;
    private long totalInstallSize = 4096; // 模拟总安装大小(MB)
    
    // 卡密相关
    private string currentCardKey = string.Empty;
    private string currentExpireDate = string.Empty;
    private int currentUsesLeft = 0;
    private string machineCode = string.Empty;
    private bool isLoggedIn = false;
    
    // 下载相关变量
    private CancellationTokenSource? downloadCancellationTokenSource;
    
    public MainWindow()
    {
        InitializeComponent();
        
        // 初始化下载进度后台工作器
        InitializeDownloadWorker();
        InitializeLocalInstallWorker();
        
        // 设置默认备份路径
        backupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "SystemBackup");
        localBackupPath = backupPath;
        BackupPathTextBox.Text = backupPath;
        LocalBackupPathTextBox.Text = backupPath;
        
        // 加载磁盘信息
        LoadDiskInfo();
        
        // 初始化卡密功能
        InitializeCardKey();
        
        // 加载系统镜像列表
        LoadSystemImagesAsync();
    }
    
    /// <summary>
    /// 加载磁盘信息
    /// </summary>
    private void LoadDiskInfo()
    {
        try
        {
            // 获取分区信息（实际应用中可显示在UI中）
            var partitions = DiskUtils.GetPartitions();
            Debug.WriteLine($"找到 {partitions.Count} 个分区:");
            
            foreach (var partition in partitions)
            {
                Debug.WriteLine(partition.ToString());
            }
        }
        catch (Exception ex)
        {
            WPF.MessageBox.Show($"加载磁盘信息失败: {ex.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
        }
    }
    
    /// <summary>
    /// 初始化下载进度后台工作器
    /// </summary>
    private void InitializeDownloadWorker()
    {
        downloadWorker = new BackgroundWorker();
        downloadWorker.WorkerReportsProgress = true;
        downloadWorker.WorkerSupportsCancellation = true;
        
        // 处理后台任务
        downloadWorker.DoWork += (sender, e) => 
        {
            // 模拟下载过程
            for (int i = 0; i <= 100; i++)
            {
                if (downloadWorker.CancellationPending)
                {
                    e.Cancel = true;
                    return;
                }
                
                // 更新进度
                downloadWorker.ReportProgress(i);
                
                // 模拟延迟
                System.Threading.Thread.Sleep(100);
            }
        };
        
        // 更新进度UI
        downloadWorker.ProgressChanged += (sender, e) => 
        {
            DownloadProgressBar.Value = e.ProgressPercentage;
            ProgressPercentage.Text = $"{e.ProgressPercentage}%";
            
            // 计算下载大小
            long currentSize = (long)(totalDownloadSize * e.ProgressPercentage / 100.0);
            DownloadSizeText.Text = $"{currentSize}MB / {totalDownloadSize}MB";
            
            if (e.ProgressPercentage < 30)
            {
                DownloadStatusText.Text = "正在准备下载...";
            }
            else if (e.ProgressPercentage < 60)
            {
                DownloadStatusText.Text = "下载系统镜像中...";
            }
            else if (e.ProgressPercentage < 90)
            {
                DownloadStatusText.Text = "验证镜像完整性...";
            }
            else
            {
                DownloadStatusText.Text = "下载完成，准备安装";
            }
        };
        
        // 下载完成
        downloadWorker.RunWorkerCompleted += (sender, e) => 
        {
            if (e.Cancelled)
            {
                DownloadStatusText.Text = "下载已取消";
                DownloadSizeText.Text = "0MB / " + totalDownloadSize + "MB";
            }
            else if (e.Error != null)
            {
                DownloadStatusText.Text = $"发生错误: {e.Error.Message}";
                WPF.MessageBox.Show($"下载过程中出现错误: {e.Error.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
            }
            else
            {
                DownloadStatusText.Text = "下载完成";
                WPF.MessageBox.Show("系统镜像已下载完成，准备开始安装。\n\n这只是一个演示，实际操作将会重启电脑并进入PE环境安装系统。", 
                               "下载完成", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Information);
            }
            
            StartButton.IsEnabled = true;
            StartButton.Content = "开始重装系统";
            StartButton.Style = (Style)FindResource("BlueButtonStyle");
        };
    }
    
    /// <summary>
    /// 初始化本地安装进度工作器
    /// </summary>
    private void InitializeLocalInstallWorker()
    {
        localInstallWorker = new BackgroundWorker();
        localInstallWorker.WorkerReportsProgress = true;
        localInstallWorker.WorkerSupportsCancellation = true;
        
        // 处理后台任务
        localInstallWorker.DoWork += (sender, e) => 
        {
            // 模拟安装过程
            for (int i = 0; i <= 100; i++)
            {
                if (localInstallWorker.CancellationPending)
                {
                    e.Cancel = true;
                    return;
                }
                
                // 更新进度
                localInstallWorker.ReportProgress(i);
                
                // 模拟延迟
                System.Threading.Thread.Sleep(50);
            }
        };
        
        // 更新进度UI
        localInstallWorker.ProgressChanged += (sender, e) => 
        {
            LocalProgressBar.Value = e.ProgressPercentage;
            LocalProgressPercentage.Text = $"{e.ProgressPercentage}%";
            
            // 计算安装大小
            long currentSize = (long)(totalInstallSize * e.ProgressPercentage / 100.0);
            LocalSizeText.Text = $"{currentSize}MB / {totalInstallSize}MB";
            
            if (e.ProgressPercentage < 30)
            {
                LocalStatusText.Text = "正在准备安装环境...";
            }
            else if (e.ProgressPercentage < 60)
            {
                LocalStatusText.Text = "正在安装系统...";
            }
            else if (e.ProgressPercentage < 90)
            {
                LocalStatusText.Text = "配置系统设置...";
            }
            else
            {
                LocalStatusText.Text = "安装完成，准备重启";
            }
        };
        
        // 安装完成
        localInstallWorker.RunWorkerCompleted += (sender, e) => 
        {
            if (e.Cancelled)
            {
                LocalStatusText.Text = "安装已取消";
                LocalSizeText.Text = $"0MB / {totalInstallSize}MB";
            }
            else if (e.Error != null)
            {
                LocalStatusText.Text = $"发生错误: {e.Error.Message}";
                WPF.MessageBox.Show($"安装过程中出现错误: {e.Error.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
            }
            else
            {
                LocalStatusText.Text = "安装完成";
                WPF.MessageBox.Show("系统安装已完成，即将重启。\n\n这只是一个演示，实际操作将会重启电脑并进入新系统。", 
                               "安装完成", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Information);
            }
            
            StartLocalButton.IsEnabled = true;
            StartLocalButton.Content = "开始重装系统";
            StartLocalButton.Style = (Style)FindResource("BlueButtonStyle");
        };
    }
    
    /// <summary>
    /// 导航按钮点击事件处理
    /// </summary>
    private void NavigationButton_Click(object sender, RoutedEventArgs e)
    {
        // 获取点击的按钮和目标面板
        var button = sender as System.Windows.Controls.RadioButton;
        if (button == null) return;
        
        string targetPanelName = button.Tag as string;
        if (string.IsNullOrEmpty(targetPanelName)) return;
        
        // 隐藏所有面板
        OnlineInstallPanel.Visibility = Visibility.Collapsed;
        LocalImagePanel.Visibility = Visibility.Collapsed;
        DisclaimerPanel.Visibility = Visibility.Collapsed;
        WebsitePanel.Visibility = Visibility.Collapsed;
        
        // 显示目标面板
        var targetPanel = FindName(targetPanelName) as UIElement;
        if (targetPanel != null)
        {
            targetPanel.Visibility = Visibility.Visible;
        }
    }
    
    /// <summary>
    /// 备份选择框勾选事件
    /// </summary>
    private void BackupCheckBox_Checked(object sender, RoutedEventArgs e)
    {
        BackupPathTextBox.IsEnabled = true;
        BrowseBackupButton.IsEnabled = true;
    }

    /// <summary>
    /// 备份选择框取消勾选事件
    /// </summary>
    private void BackupCheckBox_Unchecked(object sender, RoutedEventArgs e)
    {
        BackupPathTextBox.IsEnabled = false;
        BrowseBackupButton.IsEnabled = false;
    }
    
    /// <summary>
    /// 本地备份选择框勾选事件
    /// </summary>
    private void LocalBackupCheckBox_Checked(object sender, RoutedEventArgs e)
    {
        LocalBackupPathTextBox.IsEnabled = true;
        BrowseLocalBackupButton.IsEnabled = true;
    }

    /// <summary>
    /// 本地备份选择框取消勾选事件
    /// </summary>
    private void LocalBackupCheckBox_Unchecked(object sender, RoutedEventArgs e)
    {
        LocalBackupPathTextBox.IsEnabled = false;
        BrowseLocalBackupButton.IsEnabled = false;
    }
    
    /// <summary>
    /// 浏览备份路径按钮点击事件
    /// </summary>
    private void BrowseBackupButton_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new WinForms.FolderBrowserDialog();
        dialog.Description = "选择备份文件夹路径";
        dialog.UseDescriptionForTitle = true;
        
        if (dialog.ShowDialog() == WinForms.DialogResult.OK)
        {
            backupPath = dialog.SelectedPath;
            BackupPathTextBox.Text = backupPath;
        }
    }
    
    /// <summary>
    /// 浏览本地备份路径按钮点击事件
    /// </summary>
    private void BrowseLocalBackupButton_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new WinForms.FolderBrowserDialog();
        dialog.Description = "选择备份文件夹路径";
        dialog.UseDescriptionForTitle = true;
        
        if (dialog.ShowDialog() == WinForms.DialogResult.OK)
        {
            localBackupPath = dialog.SelectedPath;
            LocalBackupPathTextBox.Text = localBackupPath;
        }
    }
    
    /// <summary>
    /// 开始按钮点击事件处理
    /// </summary>
    private async void StartButton_Click(object sender, RoutedEventArgs e)
    {
        if (downloadWorker.IsBusy || downloadCancellationTokenSource != null)
        {
            // 取消下载
            if (downloadCancellationTokenSource != null)
            {
                downloadCancellationTokenSource.Cancel();
                DownloadStatusText.Text = "正在取消下载...";
                return;
            }
            
            downloadWorker.CancelAsync();
            return;
        }
        
        try
        {
            // 获取选中的镜像
            if (SystemImageComboBox.SelectedItem == null)
            {
                WPF.MessageBox.Show("请选择一个系统镜像", "提示", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Information);
                return;
            }
            
            // 获取镜像ID
            var selectedItem = SystemImageComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem == null || selectedItem.Tag == null)
            {
                WPF.MessageBox.Show("请选择一个有效的系统镜像", "提示", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Information);
                return;
            }
            
            int imageId = Convert.ToInt32(selectedItem.Tag);
            
            // 确认是否开始下载
            var result = WPF.MessageBox.Show("您确定要下载并安装此系统镜像吗？\n此操作将重装您的系统，请确保已备份重要数据。", 
                                      "确认安装", WPF.MessageBoxButton.YesNo, WPF.MessageBoxImage.Warning);
            
            if (result != WPF.MessageBoxResult.Yes)
            {
                return;
            }
            
            // 如果选择了备份，但未输入备份路径
            if (BackupCheckBox.IsChecked == true && string.IsNullOrEmpty(BackupPathTextBox.Text))
            {
                WPF.MessageBox.Show("请选择系统备份路径", "提示", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Information);
                return;
            }
            
            // 获取机器码
            string machineCode = MachineUtils.GetMachineCode();
            
            // 请求下载镜像
            var downloadInfo = await ApiUtils.RequestDownloadImage(imageId, currentCardKey, machineCode);
            
            if (downloadInfo.Success)
            {
                // 下载信息
                string downloadUrl = $"{ApiUtils.ApiBaseUrl}/{downloadInfo.DownloadUrl}";
                totalDownloadSize = downloadInfo.FileSize;
                
                // 更新UI
                DownloadSizeText.Text = $"0MB / {FormatSize(totalDownloadSize)}";
                ProgressPercentage.Text = "0%";
                DownloadProgressBar.Value = 0;
                DownloadStatusText.Text = "正在准备下载...";
                
                // 开始下载
                StartButton.Content = "取消安装";
                StartButton.Style = (Style)FindResource("RedButtonStyle");
                
                // 创建下载目录
                string downloadDir = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "SystemReinstaller",
                    "Downloads"
                );
                
                if (!Directory.Exists(downloadDir))
                {
                    Directory.CreateDirectory(downloadDir);
                }
                
                // 下载文件路径
                string downloadPath = Path.Combine(downloadDir, $"{downloadInfo.FileName}.iso");
                
                // 开始下载
                _ = DownloadFileAsync(downloadUrl, downloadPath);
            }
            else
            {
                WPF.MessageBox.Show($"获取下载信息失败: {downloadInfo.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            WPF.MessageBox.Show($"准备安装过程中出现错误: {ex.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
        }
    }
    
    /// <summary>
    /// 使用HttpClient异步下载文件
    /// </summary>
    private async Task DownloadFileAsync(string downloadUrl, string destinationPath)
    {
        // 创建取消令牌
        downloadCancellationTokenSource = new CancellationTokenSource();
        
        try
        {
            // 更新UI
            StartButton.IsEnabled = true; // 保持按钮可用，以便用户可以取消下载
            StartButton.Content = "取消安装";
            StartButton.Style = (Style)FindResource("RedButtonStyle");
            
            // 创建HttpClient
            using var httpClient = new HttpClient();
            
            // 设置超时时间
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            
            // 发送HEAD请求获取文件大小
            using var response = await httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead, downloadCancellationTokenSource.Token);
            response.EnsureSuccessStatusCode();
            
            // 获取文件总大小
            var totalBytes = response.Content.Headers.ContentLength ?? totalDownloadSize;
            
            // 打开文件流
            using var fileStream = new FileStream(destinationPath, FileMode.Create, FileAccess.Write, FileShare.None);
            
            // 开始下载
            using var downloadStream = await httpClient.GetStreamAsync(downloadUrl, downloadCancellationTokenSource.Token);
            
            var buffer = new byte[8192]; // 8KB缓冲区
            var totalBytesRead = 0L;
            var bytesRead = 0;
            var lastProgressUpdate = DateTime.Now;
            
            // 读取数据
            while ((bytesRead = await downloadStream.ReadAsync(buffer, 0, buffer.Length, downloadCancellationTokenSource.Token)) > 0)
            {
                // 写入文件
                await fileStream.WriteAsync(buffer, 0, bytesRead, downloadCancellationTokenSource.Token);
                
                // 更新进度
                totalBytesRead += bytesRead;
                var progressPercentage = (int)((totalBytesRead * 100) / totalBytes);
                
                // 限制UI更新频率
                if ((DateTime.Now - lastProgressUpdate).TotalMilliseconds > 100)
                {
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        UpdateDownloadProgress(progressPercentage, totalBytesRead, totalBytes);
                    });
                    lastProgressUpdate = DateTime.Now;
                }
            }
            
            // 最终更新进度
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                UpdateDownloadProgress(100, totalBytes, totalBytes);
                DownloadStatusText.Text = "下载完成";
                
                // 显示下载完成消息
                WPF.MessageBox.Show($"系统镜像已下载完成: {destinationPath}\n\n现在将准备重装系统。", 
                               "下载完成", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Information);
                
                // 开始安装系统
                StartSystemInstallation(destinationPath);
            });
        }
        catch (TaskCanceledException)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                DownloadStatusText.Text = "下载已取消";
            });
        }
        catch (OperationCanceledException)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                DownloadStatusText.Text = "下载已取消";
            });
        }
        catch (Exception ex)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                DownloadStatusText.Text = $"下载失败: {ex.Message}";
                WPF.MessageBox.Show($"启动下载失败: {ex.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
            });
        }
        finally
        {
            // 清理取消令牌
            if (downloadCancellationTokenSource != null)
            {
                downloadCancellationTokenSource.Dispose();
                downloadCancellationTokenSource = null;
            }
            
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                StartButton.IsEnabled = true;
                StartButton.Content = "开始重装系统";
                StartButton.Style = (Style)FindResource("BlueButtonStyle");
            });
        }
    }
    
    /// <summary>
    /// 更新下载进度UI
    /// </summary>
    private void UpdateDownloadProgress(int progressPercentage, long downloadedBytes, long totalBytes)
    {
        DownloadProgressBar.Value = progressPercentage;
        ProgressPercentage.Text = $"{progressPercentage}%";
        DownloadSizeText.Text = $"{FormatSize(downloadedBytes)} / {FormatSize(totalBytes)}";
        
        // 更新状态文本
        if (progressPercentage < 30)
        {
            DownloadStatusText.Text = "正在下载系统镜像...";
        }
        else if (progressPercentage < 60)
        {
            DownloadStatusText.Text = "下载系统镜像中...";
        }
        else if (progressPercentage < 90)
        {
            DownloadStatusText.Text = "验证镜像完整性...";
        }
        else
        {
            DownloadStatusText.Text = "下载完成，准备安装";
        }
    }
    
    /// <summary>
    /// 开始系统安装
    /// </summary>
    private void StartSystemInstallation(string isoPath)
    {
        try
        {
            // 这里实现实际的系统安装流程
            // 1. 创建WinPE启动环境
            // 2. 配置自动部署脚本
            // 3. 重启进入PE开始安装
            
            // 出于演示目的，这里仅显示一个消息框
            WPF.MessageBox.Show("系统即将开始安装，这只是一个演示。\n\n在实际应用中，这里将创建WinPE启动环境，并重启进入PE自动安装。", 
                           "安装准备就绪", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            WPF.MessageBox.Show($"准备系统安装环境失败: {ex.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
        }
    }
    
    /// <summary>
    /// 格式化文件大小
    /// </summary>
    private string FormatSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size = size / 1024;
        }
        
        return $"{Math.Round(size, 2)} {sizes[order]}";
    }
    
    /// <summary>
    /// 本地镜像浏览按钮点击事件
    /// </summary>
    private void BrowseButton_Click(object sender, RoutedEventArgs e)
    {
        var openFileDialog = new Microsoft.Win32.OpenFileDialog
        {
            Filter = "ISO镜像文件|*.iso|WIM镜像文件|*.wim|所有文件|*.*",
            Title = "选择系统镜像文件"
        };
        
        if (openFileDialog.ShowDialog() == true)
        {
            selectedImagePath = openFileDialog.FileName;
            LocalImagePathTextBox.Text = selectedImagePath;
            
            // 验证镜像文件
            if (!DiskUtils.VerifySystemImage(selectedImagePath))
            {
                WPF.MessageBox.Show("所选文件可能不是有效的系统镜像文件", "警告", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Warning);
            }
        }
    }
    
    /// <summary>
    /// 开始本地镜像安装按钮点击事件
    /// </summary>
    private void StartLocalButton_Click(object sender, RoutedEventArgs e)
    {
        if (localInstallWorker.IsBusy)
        {
            // 如果正在安装，则取消
            localInstallWorker.CancelAsync();
            StartLocalButton.Content = "开始重装系统";
            StartLocalButton.Style = (Style)FindResource("BlueButtonStyle");
            LocalStatusText.Text = "安装已取消";
            return;
        }
        
        // 验证是否选择了镜像文件
        if (string.IsNullOrEmpty(selectedImagePath))
        {
            WPF.MessageBox.Show("请选择系统镜像文件", "提示", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Warning);
            return;
        }
        
        // 确认开始安装
        string message = $"您选择了使用本地镜像安装系统：\n{Path.GetFileName(selectedImagePath)}\n\n";
        
        if (LocalClearDiskCheckBox.IsChecked == true)
        {
            message += "- 将会全清磁盘，格式化所有分区\n";
        }
        
        if (LocalBackupCheckBox.IsChecked == true)
        {
            message += $"- 将会备份系统文件到: {localBackupPath}\n";
        }
        
        message += "\n确定要继续吗？此操作将会准备安装环境并重启电脑。";
        
        var result = WPF.MessageBox.Show(message, "确认安装", WPF.MessageBoxButton.YesNo, WPF.MessageBoxImage.Question);
        if (result != WPF.MessageBoxResult.Yes)
        {
            return;
        }
        
        // 创建备份（如果选择了备份选项）
        if (LocalBackupCheckBox.IsChecked == true)
        {
            try
            {
                var backupResult = DiskUtils.BackupSystemFiles(localBackupPath).Result;
                if (backupResult)
                {
                    WPF.MessageBox.Show($"系统文件已备份至: {localBackupPath}", "备份完成", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                WPF.MessageBox.Show($"备份系统文件失败: {ex.Message}", "备份错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
            }
        }
        
        // 清除磁盘（如果选择了全清选项）
        if (LocalClearDiskCheckBox.IsChecked == true)
        {
            try
            {
                // 获取系统分区（这里仅作演示）
                var systemPartitions = DiskUtils.GetPartitions()
                    .Where(p => p.DriveType == DriveType.Fixed)
                    .ToList();
                
                foreach (var partition in systemPartitions)
                {
                    // 注意：这里只模拟，不实际格式化
                    bool formatResult = DiskUtils.FormatDisk(partition.DriveLetter);
                    Debug.WriteLine($"模拟格式化分区 {partition.DriveLetter}: {(formatResult ? "成功" : "失败")}");
                }
            }
            catch (Exception ex)
            {
                WPF.MessageBox.Show($"磁盘操作失败: {ex.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
            }
        }
        
        // 开始安装进度
        StartLocalButton.Content = "取消安装";
        StartLocalButton.Style = (Style)FindResource("RedButtonStyle");
        StartLocalButton.IsEnabled = true;
        localInstallWorker.RunWorkerAsync();
    }
    
    /// <summary>
    /// 访问官网按钮点击事件
    /// </summary>
    private void VisitWebsiteButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 打开默认浏览器访问官网
            Process.Start(new ProcessStartInfo
            {
                FileName = websiteUrl,
                UseShellExecute = true
            });
        }
        catch (Exception ex)
        {
            WPF.MessageBox.Show($"无法打开网站: {ex.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
        }
    }
    
    /// <summary>
    /// 初始化卡密功能
    /// </summary>
    private void InitializeCardKey()
    {
        try
        {
            // 获取本机机器码
            machineCode = MachineUtils.GetMachineCode();
            
            // 尝试从配置文件加载卡密（在实际应用中，应该从安全的位置读取）
            string configPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "SystemReinstaller",
                "config.dat");
            
            if (File.Exists(configPath))
            {
                try
                {
                    string cardKey = File.ReadAllText(configPath);
                    if (!string.IsNullOrWhiteSpace(cardKey))
                    {
                        // 尝试使用保存的卡密登录
                        CardKeyTextBox.Text = cardKey;
                        LoginAsync(cardKey);
                    }
                }
                catch
                {
                    // 忽略读取错误
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"初始化卡密功能失败: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 保存卡密到配置文件
    /// </summary>
    private void SaveCardKey(string cardKey)
    {
        try
        {
            string configDir = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "SystemReinstaller");
            
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }
            
            string configPath = Path.Combine(configDir, "config.dat");
            File.WriteAllText(configPath, cardKey);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"保存卡密失败: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 清除保存的卡密
    /// </summary>
    private void ClearSavedCardKey()
    {
        try
        {
            string configPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "SystemReinstaller",
                "config.dat");
            
            if (File.Exists(configPath))
            {
                File.Delete(configPath);
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"清除卡密失败: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 登录按钮点击事件
    /// </summary>
    private void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        string cardKey = CardKeyTextBox.Text.Trim();
        if (string.IsNullOrWhiteSpace(cardKey))
        {
            LoginStatusText.Text = "请输入卡密";
            return;
        }
        
        LoginAsync(cardKey);
    }
    
    /// <summary>
    /// 异步登录方法
    /// </summary>
    private async void LoginAsync(string cardKey)
    {
        try
        {
            LoginButton.IsEnabled = false;
            LoginStatusText.Text = "正在验证卡密...";
            
            // 验证卡密
            var result = await ApiUtils.VerifyCardKey(cardKey);
            
            if (result.Success)
            {
                // 验证成功
                currentCardKey = cardKey;
                currentExpireDate = result.ExpireDate;
                currentUsesLeft = result.UsesLeft;
                
                // 保存卡密到配置文件
                SaveCardKey(cardKey);
                
                // 检查机器码绑定状态
                var boundResult = await ApiUtils.CheckMachineBound(cardKey, machineCode);
                
                if (boundResult.Success && boundResult.IsBound)
                {
                    // 机器已绑定，登录成功后扣除使用次数
                    var deductResult = await ApiUtils.UnbindMachine(cardKey, machineCode);
                    if (deductResult.Success)
                    {
                        // 扣除次数成功，更新剩余次数
                        currentUsesLeft = Math.Max(0, currentUsesLeft - 1);
                        
                        // 重新绑定机器
                        await ApiUtils.BindMachine(cardKey, machineCode);
                        
                        // 显示账户信息
                        ShowAccountInfo();
                    }
                    else
                    {
                        LoginStatusText.Text = $"登录扣除次数失败: {deductResult.Message}";
                    }
                }
                else
                {
                    // 机器未绑定，尝试绑定
                    var bindResult = await ApiUtils.BindMachine(cardKey, machineCode);
                    
                    if (bindResult.Success)
                    {
                        // 绑定成功，扣除使用次数
                        var deductResult = await ApiUtils.UnbindMachine(cardKey, machineCode);
                        if (deductResult.Success)
                        {
                            // 扣除次数成功，更新剩余次数
                            currentUsesLeft = Math.Max(0, currentUsesLeft - 1);
                            
                            // 重新绑定机器
                            await ApiUtils.BindMachine(cardKey, machineCode);
                            
                            // 显示账户信息
                            ShowAccountInfo();
                        }
                        else
                        {
                            LoginStatusText.Text = $"登录扣除次数失败: {deductResult.Message}";
                        }
                    }
                    else
                    {
                        LoginStatusText.Text = $"绑定机器失败: {bindResult.Message}";
                    }
                }
            }
            else
            {
                // 验证失败
                LoginStatusText.Text = $"卡密验证失败: {result.Message}";
            }
        }
        catch (Exception ex)
        {
            LoginStatusText.Text = $"登录异常: {ex.Message}";
        }
        finally
        {
            LoginButton.IsEnabled = true;
        }
    }
    
    /// <summary>
    /// 显示账户信息
    /// </summary>
    private void ShowAccountInfo()
    {
        // 设置账户信息
        CardKeyInfoText.Text = currentCardKey;
        ExpireDateText.Text = Convert.ToDateTime(currentExpireDate).ToString("yyyy-MM-dd HH:mm:ss");
        UsesLeftText.Text = currentUsesLeft.ToString();
        
        // 更改登录状态文本为绿色的"登录成功"
        LoginStatusText.Text = "登录成功";
        LoginStatusText.Foreground = new System.Windows.Media.SolidColorBrush(
            (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#008000"));
        
        // 修改登录按钮样式和状态
        LoginButton.Content = "退出登录";
        LoginButton.Style = (Style)FindResource("RedButtonStyle");
        LoginButton.Click -= LoginButton_Click;
        LoginButton.Click += LogoutButton_Click;
        
        isLoggedIn = true;
    }
    
    /// <summary>
    /// 刷新按钮点击事件处理
    /// </summary>
    private async void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrEmpty(currentCardKey))
        {
            return;
        }
        
        try
        {
            RefreshButton.IsEnabled = false;
            
            // 重新验证卡密
            var result = await ApiUtils.VerifyCardKey(currentCardKey);
            
            if (result.Success)
            {
                // 更新信息
                currentExpireDate = result.ExpireDate;
                currentUsesLeft = result.UsesLeft;
                
                ExpireDateText.Text = Convert.ToDateTime(currentExpireDate).ToString("yyyy-MM-dd HH:mm:ss");
                UsesLeftText.Text = currentUsesLeft.ToString();
            }
        }
        catch (Exception ex)
        {
            WPF.MessageBox.Show($"刷新信息失败: {ex.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
        }
        finally
        {
            RefreshButton.IsEnabled = true;
        }
    }
    
    /// <summary>
    /// 解绑按钮点击事件处理
    /// </summary>
    private async void UnbindButton_Click(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrEmpty(currentCardKey) || string.IsNullOrEmpty(machineCode))
        {
            return;
        }
        
        // 确认解绑
        var result = WPF.MessageBox.Show(
            "解绑机器将会消耗一次卡密使用次数，确定要解绑吗？",
            "确认解绑",
            WPF.MessageBoxButton.YesNo,
            WPF.MessageBoxImage.Question);
        
        if (result != WPF.MessageBoxResult.Yes)
        {
            return;
        }
        
        try
        {
            // 解绑机器
            var unbindResult = await ApiUtils.UnbindMachine(currentCardKey, machineCode);
            
            if (unbindResult.Success)
            {
                WPF.MessageBox.Show("机器解绑成功", "成功", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Information);
                LogoutButton_Click(null, null);
            }
            else
            {
                WPF.MessageBox.Show($"解绑失败: {unbindResult.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            WPF.MessageBox.Show($"解绑过程中出现错误: {ex.Message}", "错误", WPF.MessageBoxButton.OK, WPF.MessageBoxImage.Error);
        }
    }
    
    /// <summary>
    /// 退出登录按钮点击事件处理
    /// </summary>
    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        // 清除当前卡密信息
        currentCardKey = string.Empty;
        currentExpireDate = string.Empty;
        currentUsesLeft = 0;
        isLoggedIn = false;
        
        // 清除UI信息
        CardKeyTextBox.Text = string.Empty;
        LoginStatusText.Text = string.Empty;
        CardKeyInfoText.Text = string.Empty;
        ExpireDateText.Text = string.Empty;
        UsesLeftText.Text = string.Empty;
        
        // 恢复登录按钮原始状态
        if (sender == LoginButton)
        {
            LoginButton.Content = "登录";
            LoginButton.Style = (Style)FindResource("BlueButtonStyle");
            LoginButton.Click -= LogoutButton_Click;
            LoginButton.Click += LoginButton_Click;
        }
        
        // 恢复登录状态文本颜色
        LoginStatusText.Foreground = new System.Windows.Media.SolidColorBrush(
            (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#FF0000"));
        
        // 清除保存的卡密
        ClearSavedCardKey();
    }

    private void WebsiteButton_Checked(object sender, RoutedEventArgs e)
    {

    }

    /// <summary>
    /// 加载系统镜像列表
    /// </summary>
    private async void LoadSystemImagesAsync()
    {
        try
        {
            // 清空下拉框
            SystemImageComboBox.Items.Clear();
            
            // 显示加载中
            var loadingItem = new ComboBoxItem();
            loadingItem.Content = "正在加载系统镜像...";
            SystemImageComboBox.Items.Add(loadingItem);
            SystemImageComboBox.SelectedItem = loadingItem;
            SystemImageComboBox.IsEnabled = false;
            
            // 获取系统镜像列表
            var result = await ApiUtils.GetSystemImages();
            
            // 清空下拉框
            SystemImageComboBox.Items.Clear();
            
            if (result.Success && result.Images.Count > 0)
            {
                foreach (var image in result.Images)
                {
                    var item = new ComboBoxItem();
                    item.Content = $"{image.Name} ({image.Size})";
                    item.Tag = image.Id;
                    SystemImageComboBox.Items.Add(item);
                }
                
                // 选择第一项
                SystemImageComboBox.SelectedIndex = 0;
            }
            else
            {
                // 没有系统镜像或请求失败
                var noImagesItem = new ComboBoxItem();
                noImagesItem.Content = result.Success ? "没有可用的系统镜像" : result.Message;
                SystemImageComboBox.Items.Add(noImagesItem);
                SystemImageComboBox.SelectedItem = noImagesItem;
            }
        }
        catch (Exception ex)
        {
            // 加载失败
            SystemImageComboBox.Items.Clear();
            
            var errorItem = new ComboBoxItem();
            errorItem.Content = "加载系统镜像失败";
            SystemImageComboBox.Items.Add(errorItem);
            SystemImageComboBox.SelectedItem = errorItem;
            
            Debug.WriteLine($"加载系统镜像失败: {ex.Message}");
        }
        finally
        {
            SystemImageComboBox.IsEnabled = true;
        }
    }
}