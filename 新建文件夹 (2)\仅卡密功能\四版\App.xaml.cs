﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace SystemReinstaller
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : System.Windows.Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // 设置应用程序的全局异常处理
            AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
            {
                Exception ex = (Exception)args.ExceptionObject;
                System.Windows.MessageBox.Show($"发生了一个未处理的异常:\n{ex.Message}\n\n{ex.StackTrace}", 
                    "应用程序错误", MessageBoxButton.OK, MessageBoxImage.Error);
            };
            
            // 应用程序级别的异常处理
            DispatcherUnhandledException += (sender, args) =>
            {
                args.Handled = true;
                System.Windows.MessageBox.Show($"发生了一个错误:\n{args.Exception.Message}", 
                    "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            };
        }
    }
}

