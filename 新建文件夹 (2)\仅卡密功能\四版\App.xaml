﻿<Application x:Class="SystemReinstaller.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:SystemReinstaller"
             xmlns:converters="clr-namespace:SystemReinstaller.Converters"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- 转换器 -->
        <converters:ProgressBarWidthConverter x:Key="ProgressBarWidthConverter"/>
        
        <!-- 颜色资源 -->
        <SolidColorBrush x:Key="MicrosoftBlue" Color="#0078D4"/>
        <SolidColorBrush x:Key="MicrosoftDarkBlue" Color="#005A9E"/>
        <SolidColorBrush x:Key="MicrosoftLightBlue" Color="#CCE5F6"/>
        <SolidColorBrush x:Key="NavBackground" Color="#0078D4"/>
        <SolidColorBrush x:Key="ContentBackground" Color="#F8F9FA"/>
        
        <!-- CheckBox样式 -->
        <Style TargetType="CheckBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <StackPanel Orientation="Horizontal">
                            <Border x:Name="checkBoxBorder" 
                                    Width="18" Height="18" 
                                    CornerRadius="3" 
                                    BorderThickness="1" 
                                    BorderBrush="#AAAAAA" 
                                    Background="White"
                                    Margin="0,0,8,0">
                                <Path x:Name="checkMark" 
                                      Width="12" Height="12" 
                                      Fill="{StaticResource MicrosoftBlue}" 
                                      Data="M1,5 L5,9 L11,1" 
                                      Stretch="Uniform" 
                                      StrokeThickness="2" 
                                      Stroke="{StaticResource MicrosoftBlue}" 
                                      Visibility="Collapsed"/>
                            </Border>
                            <ContentPresenter VerticalAlignment="Center"/>
                        </StackPanel>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="checkMark" Property="Visibility" Value="Visible"/>
                                <Setter TargetName="checkBoxBorder" Property="BorderBrush" Value="{StaticResource MicrosoftBlue}"/>
                                <Setter TargetName="checkBoxBorder" Property="Background" Value="#FFFFFF"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="checkBoxBorder" Property="BorderBrush" Value="{StaticResource MicrosoftBlue}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- TextBox样式 -->
        <Style TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" Padding="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource MicrosoftBlue}"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource MicrosoftBlue}"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- ComboBox样式 -->
        <Style TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <Border x:Name="border" 
                                    Background="{TemplateBinding Background}" 
                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                    BorderThickness="{TemplateBinding BorderThickness}" 
                                    CornerRadius="4">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <ContentPresenter x:Name="contentPresenter" 
                                                     ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" 
                                                     Content="{TemplateBinding SelectionBoxItem}" 
                                                     ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}" 
                                                     HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                                     VerticalAlignment="{TemplateBinding VerticalContentAlignment}" 
                                                     Margin="{TemplateBinding Padding}"/>
                                    <Path Grid.Column="1" 
                                          x:Name="arrow" 
                                          Fill="{StaticResource MicrosoftBlue}" 
                                          Data="M0,0 L8,0 L4,4 z" 
                                          Margin="8,0,8,0" 
                                          VerticalAlignment="Center"/>
                                </Grid>
                            </Border>
                            <Popup IsOpen="{TemplateBinding IsDropDownOpen}" 
                                  Placement="Bottom" 
                                  PopupAnimation="Slide" 
                                  AllowsTransparency="True">
                                <Border x:Name="dropDownBorder" 
                                       Background="White" 
                                       BorderBrush="{StaticResource MicrosoftBlue}" 
                                       BorderThickness="1" 
                                       CornerRadius="0,0,4,4" 
                                       Margin="0,1,0,0">
                                    <ScrollViewer Margin="0,4,0,4" 
                                                 SnapsToDevicePixels="True">
                                        <ItemsPresenter KeyboardNavigation.DirectionalNavigation="Contained"/>
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource MicrosoftBlue}"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource MicrosoftBlue}"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 导航菜单RadioButton样式 -->
        <Style x:Key="NavRadioButtonStyle" TargetType="RadioButton">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="RadioButton">
                        <Border x:Name="border" Background="#0078D4" BorderThickness="0" 
                                Padding="0" Margin="15,3" CornerRadius="4" Width="160" Height="44">
                            <TextBlock x:Name="textContent" Text="{TemplateBinding Content}" 
                                       HorizontalAlignment="Center" VerticalAlignment="Center"
                                       FontSize="16" Foreground="White"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect ShadowDepth="1" BlurRadius="3" Color="#40000000" Opacity="0.2"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="border" Property="Background" Value="White"/>
                                <Setter TargetName="textContent" Property="Foreground" Value="#0078D4"/>
                                <Setter TargetName="textContent" Property="FontWeight" Value="Bold"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        </Style>
        
        <!-- GroupBox样式 -->
        <Style TargetType="GroupBox">
            <Setter Property="BorderBrush" Value="#DEDEDE"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="GroupBox">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <Border Grid.Row="0" 
                                    Background="{StaticResource MicrosoftBlue}" 
                                    BorderThickness="1,1,1,0" 
                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                    CornerRadius="5,5,0,0" 
                                    Padding="10,6">
                                <ContentPresenter ContentSource="Header" TextElement.Foreground="White" TextElement.FontWeight="SemiBold"/>
                            </Border>
                            
                            <Border Grid.Row="1" 
                                    Background="White" 
                                    BorderThickness="1,0,1,1" 
                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                    CornerRadius="0,0,5,5" 
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter/>
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 进度条样式 -->
        <Style TargetType="ProgressBar">
            <Setter Property="Height" Value="24"/>
            <Setter Property="Foreground" Value="{StaticResource MicrosoftBlue}"/>
            <Setter Property="Background" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ProgressBar">
                        <Grid>
                            <Border Background="{TemplateBinding Background}" 
                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4"/>
                            <Border x:Name="PART_Indicator" 
                                    Background="{TemplateBinding Foreground}" 
                                    BorderThickness="0"
                                    HorizontalAlignment="Left"
                                    CornerRadius="4">
                                <Border.Width>
                                    <MultiBinding Converter="{StaticResource ProgressBarWidthConverter}">
                                        <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="Value"/>
                                        <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="Minimum"/>
                                        <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="Maximum"/>
                                        <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="ActualWidth"/>
                                    </MultiBinding>
                                </Border.Width>
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 标准按钮样式 -->
        <Style x:Key="BlueButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource MicrosoftBlue}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="25,12"/>
            <Setter Property="MinWidth" Value="200"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource MicrosoftDarkBlue}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 红色按钮样式 -->
        <Style x:Key="RedButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#E81123"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="25,12"/>
            <Setter Property="MinWidth" Value="200"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#C50F1F"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 小型按钮样式 -->
        <Style x:Key="SmallBlueButtonStyle" TargetType="Button" BasedOn="{StaticResource BlueButtonStyle}">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </Application.Resources>
</Application>
