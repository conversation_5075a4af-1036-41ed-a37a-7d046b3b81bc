﻿<Window x:Class="SystemReinstaller.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SystemReinstaller"
        xmlns:converters="clr-namespace:SystemReinstaller.Converters"
        mc:Ignorable="d"
        Title="一键重装系统" Height="620" Width="900" 
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="190"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧导航菜单 -->
        <DockPanel Grid.Column="0" Background="{StaticResource NavBackground}" LastChildFill="True">
            <!-- 顶部标题 -->
            <Border DockPanel.Dock="Top" Background="#005A9E" Padding="10,15" Margin="0,0,0,30" Height="72">
                <TextBlock Text="系统重装工具" Foreground="White" FontSize="18" FontWeight="Bold" 
                           HorizontalAlignment="Center" Margin="0,10,0,11"/>
            </Border>

            <!-- 导航按钮 - 使用StackPanel而不是UniformGrid -->
            <StackPanel VerticalAlignment="Top" Margin="0,10,0,0">
                <RadioButton x:Name="OnlineInstallButton" Content="在线重装" 
                             IsChecked="True" Tag="OnlineInstallPanel"
                             Style="{StaticResource NavRadioButtonStyle}"
                             Click="NavigationButton_Click"/>

                <RadioButton x:Name="LocalImageButton" Content="本地镜像" 
                             Tag="LocalImagePanel"
                             Style="{StaticResource NavRadioButtonStyle}"
                             Click="NavigationButton_Click"/>

                <RadioButton x:Name="DisclaimerButton" Content="免责声明" 
                             Tag="DisclaimerPanel"
                             Style="{StaticResource NavRadioButtonStyle}"
                             Click="NavigationButton_Click"/>

                <RadioButton x:Name="WebsiteButton" Content="登录" 
                             Tag="WebsitePanel"
                             Style="{StaticResource NavRadioButtonStyle}"
                             Click="NavigationButton_Click" Checked="WebsiteButton_Checked"/>
            </StackPanel>
        </DockPanel>

        <!-- 右侧内容区域 -->
        <Grid Grid.Column="1" Background="{StaticResource ContentBackground}">
            <!-- 各功能面板，根据导航菜单项选择显示 -->
            <Grid x:Name="OnlineInstallPanel" Visibility="Visible">
                <Grid Margin="20,20,20,20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*" MinHeight="80"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="在线重装系统" FontSize="20" FontWeight="Bold" Margin="0,0,0,15" 
                               Foreground="{StaticResource MicrosoftBlue}"/>

                    <!-- 系统选择 -->
                    <GroupBox Grid.Row="1" Header="选择系统镜像">
                        <StackPanel>
                            <ComboBox x:Name="SystemImageComboBox" Margin="0,8" Height="34" 
                                      FontSize="14" VerticalContentAlignment="Center">
                                <ComboBoxItem Content="Windows 11 专业版" IsSelected="True"/>
                                <ComboBoxItem Content="Windows 11 家庭版"/>
                                <ComboBoxItem Content="Windows 10 专业版"/>
                                <ComboBoxItem Content="Windows 10 家庭版"/>
                                <ComboBoxItem Content="Windows 10 LTSC"/>
                                <ComboBoxItem Content="Windows 7 专业版"/>
                            </ComboBox>
                        </StackPanel>
                    </GroupBox>

                    <!-- 操作选项 -->
                    <GroupBox Grid.Row="2" Header="操作选项">
                        <StackPanel>
                            <CheckBox x:Name="ClearDiskCheckBox" Content="全清磁盘（将格式化所有分区）" 
                                      Margin="0,8" FontSize="14"/>

                            <CheckBox x:Name="BackupCheckBox" Content="本地备份系统文件" 
                                      Margin="0,8" FontSize="14"
                                      Checked="BackupCheckBox_Checked"
                                      Unchecked="BackupCheckBox_Unchecked"/>

                            <!-- 备份路径选择 -->
                            <Grid Margin="20,8,0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox x:Name="BackupPathTextBox" 
                                         Height="32" IsReadOnly="True" 
                                         FontSize="13" VerticalContentAlignment="Center"
                                         IsEnabled="{Binding IsChecked, ElementName=BackupCheckBox}"/>
                                <Button Grid.Column="1" x:Name="BrowseBackupButton" 
                                        Content="浏览..." Margin="8,0,0,0" Padding="8,4" 
                                        Style="{StaticResource SmallBlueButtonStyle}"
                                        Click="BrowseBackupButton_Click"
                                        IsEnabled="{Binding IsChecked, ElementName=BackupCheckBox}"/>
                            </Grid>
                        </StackPanel>
                    </GroupBox>

                    <!-- 下载进度 -->
                    <GroupBox Grid.Row="3" Header="下载进度">
                        <StackPanel>
                            <Grid Margin="0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <ProgressBar x:Name="DownloadProgressBar" Height="24" Minimum="0" Maximum="100" Value="0"
                                             Foreground="{StaticResource MicrosoftBlue}"/>
                                <TextBlock Grid.Column="1" x:Name="ProgressPercentage" Text="0%" 
                                           Margin="8,0,0,0" VerticalAlignment="Center" 
                                           FontSize="14" FontWeight="SemiBold"/>
                            </Grid>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock x:Name="DownloadStatusText" Text="准备就绪" Margin="0,4" FontSize="13"/>
                                <TextBlock Grid.Column="1" x:Name="DownloadSizeText" Text="0MB / 0MB" 
                                           Margin="0,4" FontSize="13" FontWeight="SemiBold"/>
                            </Grid>
                        </StackPanel>
                    </GroupBox>

                    <!-- 开始按钮 -->
                    <Button Grid.Row="5" x:Name="StartButton" Content="开始重装系统" 
                            Margin="0,15,0,15" VerticalAlignment="Center" 
                            Width="240" Height="50" FontSize="18"
                            Style="{StaticResource BlueButtonStyle}"
                            Click="StartButton_Click"/>
                </Grid>
            </Grid>

            <Grid x:Name="LocalImagePanel" Visibility="Collapsed">
                <Grid Margin="20,20,20,20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*" MinHeight="80"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="本地镜像重装" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"
                               Foreground="{StaticResource MicrosoftBlue}"/>

                    <!-- 选择本地镜像 -->
                    <GroupBox Grid.Row="1" Header="选择本地镜像文件">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox x:Name="LocalImagePathTextBox" Margin="0,8" Height="32" 
                                     IsReadOnly="True" FontSize="13" VerticalContentAlignment="Center"/>
                            <Button Grid.Column="1" x:Name="BrowseButton" Content="浏览..." 
                                    Margin="8,8,0,8" Padding="8,4" 
                                    Style="{StaticResource SmallBlueButtonStyle}"
                                    Click="BrowseButton_Click"/>
                        </Grid>
                    </GroupBox>

                    <!-- 操作选项 -->
                    <GroupBox Grid.Row="2" Header="操作选项">
                        <StackPanel>
                            <CheckBox x:Name="LocalClearDiskCheckBox" Content="全清磁盘（将格式化所有分区）" 
                                      Margin="0,8" FontSize="14"/>

                            <CheckBox x:Name="LocalBackupCheckBox" Content="本地备份系统文件" 
                                      Margin="0,8" FontSize="14"
                                      Checked="LocalBackupCheckBox_Checked"
                                      Unchecked="LocalBackupCheckBox_Unchecked"/>

                            <!-- 备份路径选择 -->
                            <Grid Margin="20,8,0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox x:Name="LocalBackupPathTextBox" 
                                         Height="32" IsReadOnly="True" 
                                         FontSize="13" VerticalContentAlignment="Center"
                                         IsEnabled="{Binding IsChecked, ElementName=LocalBackupCheckBox}"/>
                                <Button Grid.Column="1" x:Name="BrowseLocalBackupButton" 
                                        Content="浏览..." Margin="8,0,0,0" Padding="8,4" 
                                        Style="{StaticResource SmallBlueButtonStyle}"
                                        Click="BrowseLocalBackupButton_Click"
                                        IsEnabled="{Binding IsChecked, ElementName=LocalBackupCheckBox}"/>
                            </Grid>
                        </StackPanel>
                    </GroupBox>

                    <!-- 安装进度 -->
                    <GroupBox Grid.Row="3" Header="安装进度">
                        <StackPanel>
                            <Grid Margin="0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <ProgressBar x:Name="LocalProgressBar" Height="24" Minimum="0" Maximum="100" Value="0"
                                             Foreground="{StaticResource MicrosoftBlue}"/>
                                <TextBlock Grid.Column="1" x:Name="LocalProgressPercentage" Text="0%" 
                                           Margin="8,0,0,0" VerticalAlignment="Center" 
                                           FontSize="14" FontWeight="SemiBold"/>
                            </Grid>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock x:Name="LocalStatusText" Text="准备就绪" Margin="0,4" FontSize="13"/>
                                <TextBlock Grid.Column="1" x:Name="LocalSizeText" Text="0MB / 0MB" 
                                           Margin="0,4" FontSize="13" FontWeight="SemiBold"/>
                            </Grid>
                        </StackPanel>
                    </GroupBox>

                    <!-- 开始按钮 -->
                    <Button Grid.Row="5" x:Name="StartLocalButton" Content="开始重装系统" 
                            Margin="0,15,0,15" VerticalAlignment="Center" 
                            Width="240" Height="50" FontSize="18"
                            Style="{StaticResource BlueButtonStyle}"
                            Click="StartLocalButton_Click"/>
                </Grid>
            </Grid>

            <Grid x:Name="DisclaimerPanel" Visibility="Collapsed">
                <Grid Margin="20,20,20,20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="免责声明" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"
                               Foreground="{StaticResource MicrosoftBlue}"/>
                    <TextBlock Grid.Row="1" TextWrapping="Wrap" Margin="0,8" FontSize="14" LineHeight="22">
                        <Run Text="使用须知：" FontWeight="SemiBold" FontSize="16" Foreground="{StaticResource MicrosoftBlue}"/>
                        <LineBreak/>
                        <LineBreak/>
                        <Run Text="1. 本软件仅供学习和研究使用，请勿用于商业目的。"/>
                        <LineBreak/>
                        <Run Text="2. 使用本软件进行系统重装可能导致数据丢失，请在操作前备份重要数据。"/>
                        <LineBreak/>
                        <Run Text="3. 作者不对因使用本软件而导致的任何损失负责。"/>
                        <LineBreak/>
                        <Run Text="4. 请确保您拥有所安装系统的合法授权。"/>
                        <LineBreak/>
                        <Run Text="5. 继续使用本软件视为您已同意本免责声明的全部内容。"/>
                    </TextBlock>
                </Grid>
            </Grid>

            <Grid x:Name="WebsitePanel" Visibility="Collapsed">
                <Grid Margin="20,20,20,20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="卡密登录" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"
                               Foreground="{StaticResource MicrosoftBlue}"/>

                    <StackPanel Grid.Row="1" VerticalAlignment="Top" Margin="0,0,0,20">
                        <GroupBox Header="登录信息" Margin="0,0,0,10">
                            <StackPanel Margin="10">
                                <TextBlock Text="请输入您的卡密" Margin="0,0,0,5"/>
                                <TextBox x:Name="CardKeyTextBox" Height="32" Margin="0,0,0,10" 
                                         FontSize="14" VerticalContentAlignment="Center"/>
                                <TextBlock x:Name="LoginStatusText" Foreground="Red" Margin="0,0,0,5"/>
                                <Button x:Name="LoginButton" Content="登录" Width="120" Height="32" 
                                        Style="{StaticResource BlueButtonStyle}" 
                                        Click="LoginButton_Click"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="账户信息" Margin="0,10,0,0">
                            <StackPanel Margin="10">
                                <Grid Margin="0,0,0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Text="卡密：" Grid.Column="0" Margin="0,0,10,0"/>
                                    <TextBlock x:Name="CardKeyInfoText" Grid.Column="1"/>
                                </Grid>
                                <Grid Margin="0,0,0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Text="到期时间：" Grid.Column="0" Margin="0,0,10,0"/>
                                    <TextBlock x:Name="ExpireDateText" Grid.Column="1"/>
                                </Grid>
                                <Grid Margin="0,0,0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Text="剩余次数：" Grid.Column="0" Margin="0,0,10,0"/>
                                    <TextBlock x:Name="UsesLeftText" Grid.Column="1"/>
                                </Grid>
                                <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                    <Button x:Name="RefreshButton" Content="刷新" Width="100" Height="32" 
                                            Margin="0,0,10,0" Style="{StaticResource SmallBlueButtonStyle}"
                                            Click="RefreshButton_Click"/>
                                    <Button x:Name="UnbindButton" Content="解绑机器" Width="100" Height="32" 
                                            Margin="0,0,10,0" Style="{StaticResource SmallBlueButtonStyle}"
                                            Click="UnbindButton_Click"/>
                                    <Button x:Name="LogoutButton" Content="退出登录" Width="100" Height="32" 
                                            Style="{StaticResource SmallBlueButtonStyle}"
                                            Click="LogoutButton_Click"/>
                                </StackPanel>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>

                    <StackPanel Grid.Row="2" VerticalAlignment="Center">
                        <TextBlock Text="点击下方按钮访问我们的官方网站" FontSize="16" 
                                   HorizontalAlignment="Center" Margin="0,0,0,30"/>

                        <Button x:Name="VisitWebsiteButton" Content="访问官网" 
                                Width="200" Height="50" FontSize="18"
                                Style="{StaticResource BlueButtonStyle}"
                                Click="VisitWebsiteButton_Click"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Grid>
    </Grid>
</Window>
