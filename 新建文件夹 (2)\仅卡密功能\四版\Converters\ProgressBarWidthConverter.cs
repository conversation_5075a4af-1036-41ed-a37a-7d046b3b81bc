using System;
using System.Globalization;
using System.Windows.Data;

namespace SystemReinstaller.Converters
{
    /// <summary>
    /// 计算进度条指示器的宽度
    /// </summary>
    public class ProgressBarWidthConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length < 4 ||
                !double.TryParse(values[0].ToString(), out double value) ||
                !double.TryParse(values[1].ToString(), out double minimum) ||
                !double.TryParse(values[2].ToString(), out double maximum) ||
                !double.TryParse(values[3].ToString(), out double actualWidth))
            {
                return 0;
            }

            if (maximum == minimum)
                return 0;

            return ((value - minimum) / (maximum - minimum)) * actualWidth;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 