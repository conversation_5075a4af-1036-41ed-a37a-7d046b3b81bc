<?php
// 数据库配置
$db_host = 'localhost'; // 或服务器IP，如 '127.0.0.1'
$db_user = 'tese5';      // 根据您的环境修改
$db_pass = 'tese5';          // 根据您的环境修改
$db_name = 'tese5';          // 使用已存在的数据库

// 连接数据库
try {
    $conn = mysqli_connect($db_host, $db_user, $db_pass, $db_name);

    // 检查连接
    if (!$conn) {
        throw new Exception("连接失败: " . mysqli_connect_error());
    }

    // 设置字符集
    if (!mysqli_set_charset($conn, "utf8")) {
        throw new Exception("设置字符集失败: " . mysqli_error($conn));
    }
} catch (Exception $e) {
    // 在生产环境中可能需要将错误记录到日志文件而不是直接显示
    die("数据库连接错误: " . $e->getMessage());
}
?> 