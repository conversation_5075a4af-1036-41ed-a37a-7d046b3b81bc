<?php
session_start();

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// 引入数据库配置
require_once '../config.php';

try {
    // 获取卡密列表
    $sql = "SELECT * FROM card_keys ORDER BY id DESC";
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        throw new Exception("获取卡密列表失败: " . mysqli_error($conn));
    }
    
    $cardKeys = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $cardKeys[] = $row;
    }

    // 获取绑定记录
    $sql = "SELECT * FROM machine_bindings ORDER BY id DESC";
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        throw new Exception("获取绑定记录失败: " . mysqli_error($conn));
    }
    
    $bindings = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $bindings[] = $row;
    }

    // 处理生成新卡密请求
    if (isset($_POST['generate_key'])) {
        $expireDays = isset($_POST['expire_days']) ? intval($_POST['expire_days']) : 30;
        $totalUses = isset($_POST['total_uses']) ? intval($_POST['total_uses']) : 10;
        
        // 生成随机卡密
        $cardKey = generateCardKey();
        $expireDate = date('Y-m-d H:i:s', strtotime("+$expireDays days"));
        
        $stmt = mysqli_prepare($conn, "INSERT INTO card_keys (card_key, expire_date, total_uses, uses_left) VALUES (?, ?, ?, ?)");
        
        if ($stmt === false) {
            throw new Exception("准备SQL语句失败: " . mysqli_error($conn));
        }
        
        if (!mysqli_stmt_bind_param($stmt, "ssii", $cardKey, $expireDate, $totalUses, $totalUses)) {
            throw new Exception("绑定参数失败: " . mysqli_stmt_error($stmt));
        }
        
        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception("执行SQL失败: " . mysqli_stmt_error($stmt));
        }
        
        $message = "新卡密 $cardKey 生成成功";
        // 刷新页面
        header('Location: index.php?message=' . urlencode($message));
        exit;
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

// 生成随机卡密
function generateCardKey() {
    $chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $key = '';
    
    // 格式: XXXX-XXXX-XXXX-XXXX
    for ($i = 0; $i < 4; $i++) {
        for ($j = 0; $j < 4; $j++) {
            $key .= $chars[rand(0, strlen($chars) - 1)];
        }
        if ($i < 3) $key .= '-';
    }
    
    return $key;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统重装工具 - 管理后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <?php include 'navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>卡密管理</h2>
        </div>
        
        <?php if (isset($_GET['message'])): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($_GET['message']); ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h4>生成新卡密</h4>
            </div>
            <div class="card-body">
                <form method="post" class="row g-3">
                    <div class="col-md-4">
                        <label for="expire_days" class="form-label">有效期(天)</label>
                        <input type="number" class="form-control" id="expire_days" name="expire_days" value="30" min="1">
                    </div>
                    <div class="col-md-4">
                        <label for="total_uses" class="form-label">使用次数</label>
                        <input type="number" class="form-control" id="total_uses" name="total_uses" value="10" min="1">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" name="generate_key" class="btn btn-primary">生成卡密</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h4>卡密列表</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>卡密</th>
                                <th>到期时间</th>
                                <th>总次数</th>
                                <th>剩余次数</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cardKeys as $key): ?>
                                <tr>
                                    <td><?php echo $key['id']; ?></td>
                                    <td><?php echo htmlspecialchars($key['card_key']); ?></td>
                                    <td><?php echo $key['expire_date']; ?></td>
                                    <td><?php echo $key['total_uses']; ?></td>
                                    <td><?php echo $key['uses_left']; ?></td>
                                    <td><?php echo $key['create_time']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                            <?php if (empty($cardKeys)): ?>
                                <tr>
                                    <td colspan="6" class="text-center">暂无数据</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h4>机器绑定记录</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>卡密</th>
                                <th>机器码</th>
                                <th>绑定时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($bindings as $binding): ?>
                                <tr>
                                    <td><?php echo $binding['id']; ?></td>
                                    <td><?php echo htmlspecialchars($binding['card_key']); ?></td>
                                    <td><?php echo htmlspecialchars($binding['machine_code']); ?></td>
                                    <td><?php echo $binding['bind_time']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                            <?php if (empty($bindings)): ?>
                                <tr>
                                    <td colspan="4" class="text-center">暂无数据</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 