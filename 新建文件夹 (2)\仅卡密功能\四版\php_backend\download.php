<?php
// 包含数据库配置
require_once 'config.php';

// 获取参数
$imageId = $_GET['id'] ?? 0;
$logId = $_GET['log_id'] ?? 0;

// 简单验证
if (empty($imageId) || empty($logId)) {
    header('HTTP/1.0 400 Bad Request');
    echo "无效的请求参数";
    exit;
}

// 查询下载日志，验证下载请求合法性
$stmt = mysqli_prepare($conn, "SELECT dl.*, si.file_path, si.name, si.version, si.is_remote 
                               FROM download_logs dl 
                               JOIN system_images si ON dl.system_image_id = si.id 
                               WHERE dl.id = ? AND dl.system_image_id = ?");

if ($stmt === false) {
    header('HTTP/1.0 500 Internal Server Error');
    echo "服务器错误: " . mysqli_error($conn);
    exit;
}

mysqli_stmt_bind_param($stmt, "ii", $logId, $imageId);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if ($download = mysqli_fetch_assoc($result)) {
    mysqli_stmt_close($stmt);
    
    // 更新下载状态
    $updateStmt = mysqli_prepare($conn, "UPDATE download_logs SET status = 1, completed_at = NOW() WHERE id = ?");
    
    if ($updateStmt !== false) {
        mysqli_stmt_bind_param($updateStmt, "i", $logId);
        mysqli_stmt_execute($updateStmt);
        mysqli_stmt_close($updateStmt);
    }
    
    // 设置文件名
    $filename = $download['name'] . ' ' . $download['version'] . '.iso';
    
    // 检查是否是远程文件
    if ($download['is_remote'] == 1) {
        // 这是远程直链文件，重定向到原始URL
        $remote_url = $download['file_path'];
        
        // 记录访问日志
        error_log("Redirecting to remote ISO: " . $remote_url);
        
        // 重定向到远程URL
        header('Location: ' . $remote_url);
        exit;
    } else {
        // 这是本地上传的文件
        $file_path = __DIR__ . '/uploads/images/' . $download['file_path'];
        
        // 检查文件是否存在
        if (!file_exists($file_path)) {
            header('HTTP/1.0 404 Not Found');
            echo "文件未找到";
            exit;
        }
        
        // 设置响应头，开始下载
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file_path));
        
        // 发送文件内容
        readfile($file_path);
        exit;
    }
} else {
    mysqli_stmt_close($stmt);
    
    header('HTTP/1.0 403 Forbidden');
    echo "无效的下载请求";
    exit;
} 