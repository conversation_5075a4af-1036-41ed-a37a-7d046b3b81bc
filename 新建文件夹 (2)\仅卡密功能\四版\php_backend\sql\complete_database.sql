-- 创建卡密表
CREATE TABLE IF NOT EXISTS card_keys (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_key VARCHAR(100) NOT NULL UNIQUE COMMENT '卡密',
    expire_date DATETIME NOT NULL COMMENT '过期日期',
    total_uses INT NOT NULL DEFAULT 1 COMMENT '总使用次数',
    uses_left INT NOT NULL DEFAULT 1 COMMENT '剩余使用次数',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡密表';

-- 创建机器绑定表
CREATE TABLE IF NOT EXISTS machine_bindings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_key VARCHAR(100) NOT NULL COMMENT '卡密',
    machine_code VARCHAR(100) NOT NULL COMMENT '机器码',
    bind_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
    UNIQUE KEY (card_key, machine_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器绑定表';

-- 创建系统镜像表（已包含is_remote字段）
CREATE TABLE IF NOT EXISTS system_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '镜像名称',
    version VARCHAR(50) NOT NULL COMMENT '系统版本',
    description TEXT COMMENT '镜像描述',
    file_path VARCHAR(255) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    is_remote TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为远程镜像链接',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统镜像表';

-- 创建镜像下载记录表
CREATE TABLE IF NOT EXISTS download_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    card_key_id INT COMMENT '卡密ID',
    system_image_id INT COMMENT '镜像ID',
    machine_code VARCHAR(100) COMMENT '机器码',
    ip VARCHAR(50) COMMENT 'IP地址',
    status TINYINT(1) DEFAULT 0 COMMENT '下载状态: 0开始, 1完成, 2失败',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    FOREIGN KEY (card_key_id) REFERENCES card_keys(id),
    FOREIGN KEY (system_image_id) REFERENCES system_images(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='镜像下载记录表';

-- 创建管理员账户表
CREATE TABLE IF NOT EXISTS admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员账户表';

-- 插入默认管理员账户 (密码: admin123)
INSERT INTO admins (username, password) VALUES ('admin', '$2y$10$WJzjWG2C9nCwqOnH3D5vwetGG5XEpIyxdXQKrV.HZT9JQA2g4UtHG'); 