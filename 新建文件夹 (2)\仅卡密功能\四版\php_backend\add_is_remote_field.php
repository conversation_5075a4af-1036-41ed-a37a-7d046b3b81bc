<?php
// 包含数据库配置
require_once 'config.php';

echo "开始向system_images表添加is_remote字段...\n";

// 检查字段是否已存在
$checkSql = "SHOW COLUMNS FROM system_images LIKE 'is_remote'";
$result = mysqli_query($conn, $checkSql);

if (mysqli_num_rows($result) > 0) {
    echo "is_remote字段已存在，无需添加。\n";
} else {
    // 添加is_remote字段
    $addSql = "ALTER TABLE system_images ADD COLUMN is_remote TINYINT(1) NOT NULL DEFAULT 0 AFTER is_active";
    if (mysqli_query($conn, $addSql)) {
        echo "成功添加is_remote字段！\n";
    } else {
        echo "添加字段失败: " . mysqli_error($conn) . "\n";
    }
}

// 查看当前表结构
echo "\n当前system_images表结构：\n";
$descSql = "DESCRIBE system_images";
$result = mysqli_query($conn, $descSql);

if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        echo $row['Field'] . " - " . $row['Type'] . " - " . ($row['Null'] == 'YES' ? '可为NULL' : '不可为NULL') . " - " . $row['Default'] . "\n";
    }
} else {
    echo "无法获取表结构: " . mysqli_error($conn) . "\n";
}

echo "\n操作完成。\n";
?> 