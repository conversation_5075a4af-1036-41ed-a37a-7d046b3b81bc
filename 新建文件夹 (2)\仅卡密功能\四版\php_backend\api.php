<?php
header('Content-Type: application/json');
require_once 'config.php';

// 获取操作类型
$action = isset($_POST['action']) ? $_POST['action'] : '';

// 根据操作类型执行相应的功能
switch ($action) {
    case 'verify_key':
        verifyCardKey();
        break;
    case 'bind_machine':
        bindMachine();
        break;
    case 'unbind_machine':
        unbindMachine();
        break;
    case 'check_machine':
        checkMachineBound();
        break;
    case 'get_system_images':
        getSystemImages();
        break;
    case 'getSystemImageDetail':
        $imageId = $_REQUEST['image_id'] ?? 0;
        getSystemImageDetail($imageId);
        break;
    case 'uploadSystemImage':
        uploadSystemImage();
        break;
    case 'updateSystemImageStatus':
        $imageId = $_REQUEST['image_id'] ?? 0;
        $isActive = $_REQUEST['is_active'] ?? 0;
        updateSystemImageStatus($imageId, $isActive);
        break;
    case 'deleteSystemImage':
        $imageId = $_REQUEST['image_id'] ?? 0;
        deleteSystemImage($imageId);
        break;
    case 'downloadSystemImage':
        $imageId = $_REQUEST['image_id'] ?? 0;
        $cardKey = $_REQUEST['card_key'] ?? '';
        $machineCode = $_REQUEST['machine_code'] ?? '';
        downloadSystemImage($imageId, $cardKey, $machineCode);
        break;
    case 'request_download':
        $imageId = $_REQUEST['image_id'] ?? 0;
        $cardKey = $_REQUEST['card_key'] ?? '';
        $machineCode = $_REQUEST['machine_code'] ?? '';
        requestDownloadImage($imageId, $cardKey, $machineCode);
        break;
    default:
        echo json_encode(['status' => 'error', 'message' => '未知操作']);
}

// 验证卡密
function verifyCardKey() {
    global $conn;
    
    $cardKey = isset($_POST['card_key']) ? $_POST['card_key'] : '';
    
    if (empty($cardKey)) {
        echo json_encode(['status' => 'error', 'message' => '卡密不能为空']);
        return;
    }
    
    // 查询卡密
    $stmt = mysqli_prepare($conn, "SELECT id, expire_date, uses_left FROM card_keys WHERE card_key = ?");
    
    if ($stmt === false) {
        echo json_encode(['status' => 'error', 'message' => '查询卡密失败: ' . mysqli_error($conn)]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "s", $cardKey);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_store_result($stmt);
    
    if (mysqli_stmt_num_rows($stmt) == 0) {
        echo json_encode(['status' => 'error', 'message' => '卡密不存在']);
        return;
    }
    
    mysqli_stmt_bind_result($stmt, $id, $expireDate, $usesLeft);
    mysqli_stmt_fetch($stmt);
    
    // 检查是否过期
    if (strtotime($expireDate) < time()) {
        echo json_encode(['status' => 'error', 'message' => '卡密已过期']);
        return;
    }
    
    // 检查剩余使用次数
    if ($usesLeft <= 0) {
        echo json_encode(['status' => 'error', 'message' => '卡密已无可用次数']);
        return;
    }
    
    echo json_encode([
        'status' => 'success',
        'expire_date' => $expireDate,
        'uses_left' => $usesLeft
    ]);
}

// 绑定机器码
function bindMachine() {
    global $conn;
    
    $cardKey = isset($_POST['card_key']) ? $_POST['card_key'] : '';
    $machineCode = isset($_POST['machine_code']) ? $_POST['machine_code'] : '';
    
    if (empty($cardKey) || empty($machineCode)) {
        echo json_encode(['status' => 'error', 'message' => '卡密或机器码不能为空']);
        return;
    }
    
    // 检查卡密是否有效
    $stmt = mysqli_prepare($conn, "SELECT id FROM card_keys WHERE card_key = ? AND expire_date > NOW() AND uses_left > 0");
    
    if ($stmt === false) {
        echo json_encode(['status' => 'error', 'message' => '查询卡密失败: ' . mysqli_error($conn)]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "s", $cardKey);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_store_result($stmt);
    
    if (mysqli_stmt_num_rows($stmt) == 0) {
        echo json_encode(['status' => 'error', 'message' => '卡密无效']);
        return;
    }
    
    // 检查是否已绑定
    $stmt = mysqli_prepare($conn, "SELECT id FROM machine_bindings WHERE card_key = ? AND machine_code = ?");
    
    if ($stmt === false) {
        echo json_encode(['status' => 'error', 'message' => '查询绑定记录失败: ' . mysqli_error($conn)]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "ss", $cardKey, $machineCode);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_store_result($stmt);
    
    if (mysqli_stmt_num_rows($stmt) > 0) {
        echo json_encode(['status' => 'success', 'message' => '该机器已绑定此卡密']);
        return;
    }
    
    // 添加绑定记录
    $stmt = mysqli_prepare($conn, "INSERT INTO machine_bindings (card_key, machine_code) VALUES (?, ?)");
    
    if ($stmt === false) {
        echo json_encode(['status' => 'error', 'message' => '准备绑定语句失败: ' . mysqli_error($conn)]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "ss", $cardKey, $machineCode);
    
    if (mysqli_stmt_execute($stmt)) {
        echo json_encode(['status' => 'success', 'message' => '机器绑定成功']);
    } else {
        echo json_encode(['status' => 'error', 'message' => '绑定失败: ' . mysqli_error($conn)]);
    }
}

// 解绑机器码
function unbindMachine() {
    global $conn;
    
    $cardKey = isset($_POST['card_key']) ? $_POST['card_key'] : '';
    $machineCode = isset($_POST['machine_code']) ? $_POST['machine_code'] : '';
    
    if (empty($cardKey) || empty($machineCode)) {
        echo json_encode(['status' => 'error', 'message' => '卡密或机器码不能为空']);
        return;
    }
    
    // 开始事务
    mysqli_begin_transaction($conn);
    
    try {
        // 检查绑定是否存在
        $stmt = mysqli_prepare($conn, "SELECT id FROM machine_bindings WHERE card_key = ? AND machine_code = ?");
        
        if ($stmt === false) {
            echo json_encode(['status' => 'error', 'message' => '查询绑定记录失败: ' . mysqli_error($conn)]);
            mysqli_rollback($conn);
            return;
        }
        
        mysqli_stmt_bind_param($stmt, "ss", $cardKey, $machineCode);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_store_result($stmt);
        
        if (mysqli_stmt_num_rows($stmt) == 0) {
            echo json_encode(['status' => 'error', 'message' => '该机器未绑定此卡密']);
            mysqli_rollback($conn);
            return;
        }
        
        // 删除绑定记录
        $stmt = mysqli_prepare($conn, "DELETE FROM machine_bindings WHERE card_key = ? AND machine_code = ?");
        
        if ($stmt === false) {
            echo json_encode(['status' => 'error', 'message' => '准备删除绑定语句失败: ' . mysqli_error($conn)]);
            mysqli_rollback($conn);
            return;
        }
        
        mysqli_stmt_bind_param($stmt, "ss", $cardKey, $machineCode);
        mysqli_stmt_execute($stmt);
        
        // 减少使用次数
        $stmt = mysqli_prepare($conn, "UPDATE card_keys SET uses_left = uses_left - 1 WHERE card_key = ? AND uses_left > 0");
        
        if ($stmt === false) {
            echo json_encode(['status' => 'error', 'message' => '准备更新次数语句失败: ' . mysqli_error($conn)]);
            mysqli_rollback($conn);
            return;
        }
        
        mysqli_stmt_bind_param($stmt, "s", $cardKey);
        mysqli_stmt_execute($stmt);
        
        if (mysqli_stmt_affected_rows($stmt) == 0) {
            echo json_encode(['status' => 'error', 'message' => '无可用次数']);
            mysqli_rollback($conn);
            return;
        }
        
        // 提交事务
        mysqli_commit($conn);
        echo json_encode(['status' => 'success', 'message' => '解绑成功']);
    } catch (Exception $e) {
        mysqli_rollback($conn);
        echo json_encode(['status' => 'error', 'message' => '解绑失败: ' . $e->getMessage()]);
    }
}

// 检查机器是否已绑定
function checkMachineBound() {
    global $conn;
    
    $cardKey = isset($_POST['card_key']) ? $_POST['card_key'] : '';
    $machineCode = isset($_POST['machine_code']) ? $_POST['machine_code'] : '';
    
    if (empty($cardKey) || empty($machineCode)) {
        echo json_encode(['status' => 'error', 'message' => '卡密或机器码不能为空']);
        return;
    }
    
    // 检查是否已绑定
    $stmt = mysqli_prepare($conn, "SELECT id FROM machine_bindings WHERE card_key = ? AND machine_code = ?");
    
    if ($stmt === false) {
        echo json_encode(['status' => 'error', 'message' => '查询绑定记录失败: ' . mysqli_error($conn)]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "ss", $cardKey, $machineCode);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_store_result($stmt);
    
    if (mysqli_stmt_num_rows($stmt) > 0) {
        echo json_encode(['status' => 'success', 'is_bound' => true]);
    } else {
        echo json_encode(['status' => 'success', 'is_bound' => false]);
    }
}

// 获取所有系统镜像
function getSystemImages() {
    global $conn;
    
    $stmt = mysqli_prepare($conn, "SELECT id, name, version, description, file_size, download_count, is_active FROM system_images WHERE is_active = 1 ORDER BY created_at DESC");
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => '查询系统镜像失败: ' . mysqli_error($conn)
        ]);
        return;
    }
    
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $images = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // 格式化文件大小为字符串
        if (isset($row['file_size'])) {
            $row['size'] = formatFileSize($row['file_size']);
        } else {
            $row['size'] = '未知';
        }
        $images[] = $row;
    }
    
    mysqli_stmt_close($stmt);
    
    echo json_encode([
        'status' => 'success',
        'images' => $images
    ]);
}

// 获取单个系统镜像详情
function getSystemImageDetail($imageId) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, "SELECT * FROM system_images WHERE id = ?");
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => '查询系统镜像失败: ' . mysqli_error($conn)
        ]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "i", $imageId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($image = mysqli_fetch_assoc($result)) {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'success',
            'data' => $image
        ]);
    } else {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'error',
            'message' => '找不到指定的系统镜像'
        ]);
    }
}

// 上传系统镜像（管理员功能）
function uploadSystemImage() {
    global $conn;
    
    // 检查是否有上传错误
    if ($_FILES['image_file']['error'] > 0) {
        echo json_encode([
            'status' => 'error',
            'message' => '文件上传失败: ' . $_FILES['image_file']['error']
        ]);
        return;
    }
    
    // 获取表单数据
    $name = $_POST['name'] ?? '';
    $version = $_POST['version'] ?? '';
    $description = $_POST['description'] ?? '';
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // 检查必填字段
    if (empty($name) || empty($version)) {
        echo json_encode([
            'status' => 'error',
            'message' => '镜像名称和版本不能为空'
        ]);
        return;
    }
    
    // 获取文件信息
    $file_name = $_FILES['image_file']['name'];
    $file_size = $_FILES['image_file']['size'];
    $file_tmp = $_FILES['image_file']['tmp_name'];
    $file_type = $_FILES['image_file']['type'];
    $file_hash = hash_file('sha256', $file_tmp);
    
    // 创建存储目录
    $upload_dir = __DIR__ . '/uploads/images/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // 生成唯一文件名
    $new_file_name = uniqid() . '_' . $file_name;
    $file_path = $upload_dir . $new_file_name;
    
    // 保存文件
    if (!move_uploaded_file($file_tmp, $file_path)) {
        echo json_encode([
            'status' => 'error',
            'message' => '保存文件失败'
        ]);
        return;
    }
    
    // 保存到数据库
    $stmt = mysqli_prepare($conn, "INSERT INTO system_images (name, version, description, file_path, file_size, file_hash, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => '保存镜像信息失败: ' . mysqli_error($conn)
        ]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "ssssdsi", $name, $version, $description, $new_file_name, $file_size, $file_hash, $is_active);
    
    if (mysqli_stmt_execute($stmt)) {
        $image_id = mysqli_insert_id($conn);
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'success',
            'message' => '系统镜像上传成功',
            'data' => [
                'id' => $image_id,
                'name' => $name,
                'file_name' => $new_file_name
            ]
        ]);
    } else {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'error',
            'message' => '保存镜像信息失败: ' . mysqli_error($conn)
        ]);
    }
}

// 更新系统镜像状态（管理员功能）
function updateSystemImageStatus($imageId, $isActive) {
    global $conn;
    
    $status = $isActive ? 1 : 0;
    
    $stmt = mysqli_prepare($conn, "UPDATE system_images SET is_active = ? WHERE id = ?");
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => '更新镜像状态失败: ' . mysqli_error($conn)
        ]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "ii", $status, $imageId);
    
    if (mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'success',
            'message' => '镜像状态已更新'
        ]);
    } else {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'error',
            'message' => '更新镜像状态失败: ' . mysqli_error($conn)
        ]);
    }
}

// 删除系统镜像（管理员功能）
function deleteSystemImage($imageId) {
    global $conn;
    
    // 首先获取镜像信息
    $stmt = mysqli_prepare($conn, "SELECT file_path FROM system_images WHERE id = ?");
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => '查询系统镜像失败: ' . mysqli_error($conn)
        ]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "i", $imageId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($image = mysqli_fetch_assoc($result)) {
        mysqli_stmt_close($stmt);
        
        // 删除文件
        $file_path = __DIR__ . '/uploads/images/' . $image['file_path'];
        if (file_exists($file_path)) {
            unlink($file_path);
        }
        
        // 删除数据库记录
        $stmt = mysqli_prepare($conn, "DELETE FROM system_images WHERE id = ?");
        
        if ($stmt === false) {
            echo json_encode([
                'status' => 'error',
                'message' => '删除镜像记录失败: ' . mysqli_error($conn)
            ]);
            return;
        }
        
        mysqli_stmt_bind_param($stmt, "i", $imageId);
        
        if (mysqli_stmt_execute($stmt)) {
            mysqli_stmt_close($stmt);
            
            echo json_encode([
                'status' => 'success',
                'message' => '系统镜像已删除'
            ]);
        } else {
            mysqli_stmt_close($stmt);
            
            echo json_encode([
                'status' => 'error',
                'message' => '删除镜像记录失败: ' . mysqli_error($conn)
            ]);
        }
    } else {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'error',
            'message' => '找不到指定的系统镜像'
        ]);
    }
}

// 格式化文件大小
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return round($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return round($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return round($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}

// 增加下载计数
function incrementDownloadCount($imageId) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, "UPDATE system_images SET download_count = download_count + 1 WHERE id = ?");
    
    if ($stmt === false) {
        return false;
    }
    
    mysqli_stmt_bind_param($stmt, "i", $imageId);
    $result = mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
    
    return $result;
}

// 记录下载日志
function logDownload($cardKeyId, $imageId, $machineCode, $ip) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, "INSERT INTO download_logs (card_key_id, system_image_id, machine_code, ip, status) VALUES (?, ?, ?, ?, 0)");
    
    if ($stmt === false) {
        return false;
    }
    
    mysqli_stmt_bind_param($stmt, "iiss", $cardKeyId, $imageId, $machineCode, $ip);
    $result = mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
    
    return $result ? mysqli_insert_id($conn) : false;
}

// 下载系统镜像
function downloadSystemImage($imageId, $cardKey, $machineCode) {
    global $conn;
    
    // 验证卡密
    $verifyResult = verifyCardKey($cardKey);
    $cardKeyData = json_decode($verifyResult, true);
    
    if ($cardKeyData['status'] != 'success') {
        echo json_encode([
            'status' => 'error',
            'message' => '卡密验证失败: ' . $cardKeyData['message']
        ]);
        return;
    }
    
    // 检查使用次数
    if ($cardKeyData['data']['uses_left'] <= 0) {
        echo json_encode([
            'status' => 'error',
            'message' => '卡密使用次数已用完'
        ]);
        return;
    }
    
    // 获取镜像信息
    $stmt = mysqli_prepare($conn, "SELECT * FROM system_images WHERE id = ? AND is_active = 1");
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => '查询系统镜像失败: ' . mysqli_error($conn)
        ]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "i", $imageId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($image = mysqli_fetch_assoc($result)) {
        mysqli_stmt_close($stmt);
        
        // 减少卡密使用次数
        $cardKeyId = $cardKeyData['data']['id'];
        $usesLeft = $cardKeyData['data']['uses_left'] - 1;
        
        $updateStmt = mysqli_prepare($conn, "UPDATE card_keys SET uses_left = ? WHERE id = ?");
        
        if ($updateStmt === false) {
            echo json_encode([
                'status' => 'error',
                'message' => '更新卡密使用次数失败: ' . mysqli_error($conn)
            ]);
            return;
        }
        
        mysqli_stmt_bind_param($updateStmt, "ii", $usesLeft, $cardKeyId);
        mysqli_stmt_execute($updateStmt);
        mysqli_stmt_close($updateStmt);
        
        // 增加下载计数
        incrementDownloadCount($imageId);
        
        // 记录下载日志
        $ip = $_SERVER['REMOTE_ADDR'];
        $logId = logDownload($cardKeyId, $imageId, $machineCode, $ip);
        
        // 返回下载信息
        echo json_encode([
            'status' => 'success',
            'message' => '准备下载系统镜像',
            'data' => [
                'download_url' => 'download.php?id=' . $imageId . '&log_id=' . $logId,
                'file_size' => $image['file_size'],
                'file_name' => $image['name'] . ' ' . $image['version']
            ]
        ]);
    } else {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'error',
            'message' => '找不到指定的系统镜像或镜像已禁用'
        ]);
    }
}

// 请求下载图像
function requestDownloadImage($imageId, $cardKey, $machineCode) {
    global $conn;
    
    // 验证参数
    if (empty($imageId) || empty($cardKey) || empty($machineCode)) {
        echo json_encode([
            'status' => 'error',
            'message' => '参数错误，请提供镜像ID、卡密和机器码'
        ]);
        return;
    }
    
    // 验证卡密
    $stmt = mysqli_prepare($conn, "SELECT id, expire_date, uses_left FROM card_keys WHERE card_key = ?");
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => '查询卡密失败: ' . mysqli_error($conn)
        ]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "s", $cardKey);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_store_result($stmt);
    
    if (mysqli_stmt_num_rows($stmt) == 0) {
        echo json_encode([
            'status' => 'error',
            'message' => '卡密不存在'
        ]);
        return;
    }
    
    $cardKeyId = 0;
    $expireDate = '';
    $usesLeft = 0;
    
    mysqli_stmt_bind_result($stmt, $cardKeyId, $expireDate, $usesLeft);
    mysqli_stmt_fetch($stmt);
    mysqli_stmt_close($stmt);
    
    // 检查是否过期
    if (strtotime($expireDate) < time()) {
        echo json_encode([
            'status' => 'error',
            'message' => '卡密已过期'
        ]);
        return;
    }
    
    // 检查剩余使用次数
    if ($usesLeft <= 0) {
        echo json_encode([
            'status' => 'error',
            'message' => '卡密已无可用次数'
        ]);
        return;
    }
    
    // 获取镜像信息
    $stmt = mysqli_prepare($conn, "SELECT * FROM system_images WHERE id = ? AND is_active = 1");
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => '查询系统镜像失败: ' . mysqli_error($conn)
        ]);
        return;
    }
    
    mysqli_stmt_bind_param($stmt, "i", $imageId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($image = mysqli_fetch_assoc($result)) {
        mysqli_stmt_close($stmt);
        
        // 记录下载日志
        $ip = $_SERVER['REMOTE_ADDR'];
        $logId = logDownload($cardKeyId, $imageId, $machineCode, $ip);
        
        if ($logId === false) {
            echo json_encode([
                'status' => 'error',
                'message' => '记录下载日志失败'
            ]);
            return;
        }
        
        // 返回下载信息
        echo json_encode([
            'status' => 'success',
            'download_url' => 'download.php?id=' . $imageId . '&log_id=' . $logId,
            'file_name' => $image['name'] . ' ' . $image['version'],
            'file_size' => $image['file_size']
        ]);
    } else {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'error',
            'message' => '找不到指定的系统镜像或镜像已禁用'
        ]);
    }
}
?> 