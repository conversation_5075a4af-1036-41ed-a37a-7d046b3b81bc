<?php
// 数据库配置
$db_host = 'localhost'; // 或服务器IP
$db_user = 'demo01';      // 根据您的环境修改
$db_pass = 'demo01';          // 根据您的环境修改
$db_name = 'demo01';     // 使用已存在的数据库

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // 连接数据库
    $conn = mysqli_connect($db_host, $db_user, $db_pass, $db_name);

    // 检查连接
    if (!$conn) {
        throw new Exception("连接失败: " . mysqli_connect_error());
    }

    // 不再尝试创建数据库，直接使用已有的数据库
    echo "使用数据库: $db_name<br>";

    // 创建卡密表
    $sql = "CREATE TABLE IF NOT EXISTS card_keys (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        card_key VARCHAR(50) NOT NULL UNIQUE,
        expire_date DATETIME NOT NULL,
        total_uses INT(11) NOT NULL DEFAULT 10,
        uses_left INT(11) NOT NULL DEFAULT 10,
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP
    )";

    if (mysqli_query($conn, $sql)) {
        echo "卡密表创建成功<br>";
    } else {
        throw new Exception("创建卡密表错误: " . mysqli_error($conn));
    }

    // 创建机器绑定表
    $sql = "CREATE TABLE IF NOT EXISTS machine_bindings (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        card_key VARCHAR(50) NOT NULL,
        machine_code VARCHAR(50) NOT NULL,
        bind_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(card_key, machine_code)
    )";

    if (mysqli_query($conn, $sql)) {
        echo "机器绑定表创建成功<br>";
    } else {
        throw new Exception("创建机器绑定表错误: " . mysqli_error($conn));
    }

    // 创建示例卡密数据
    $expireDate = date('Y-m-d H:i:s', strtotime('+30 days'));
    $sql = "INSERT IGNORE INTO card_keys (card_key, expire_date, total_uses, uses_left) 
            VALUES ('DEMO-1234-5678-9ABC', '$expireDate', 10, 10)";

    if (mysqli_query($conn, $sql)) {
        echo "示例卡密创建成功<br>";
    } else {
        throw new Exception("创建示例卡密错误: " . mysqli_error($conn));
    }

    echo "<h2>数据库初始化完成!</h2>";

    // 显示数据库连接信息
    echo "<h3>数据库连接信息:</h3>";
    echo "<pre>";
    echo "Host: $db_host\n";
    echo "User: $db_user\n";
    echo "Database: $db_name\n";
    echo "</pre>";

    // 关闭连接
    mysqli_close($conn);
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold;'>";
    echo "初始化失败: " . $e->getMessage();
    echo "</div>";
    
    if (isset($conn)) {
        mysqli_close($conn);
    }
}
?> 