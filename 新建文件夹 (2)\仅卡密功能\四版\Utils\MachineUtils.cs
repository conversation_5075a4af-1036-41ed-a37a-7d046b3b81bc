using System;
using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace SystemReinstaller.Utils
{
    /// <summary>
    /// 机器相关的工具类
    /// </summary>
    public static class MachineUtils
    {
        /// <summary>
        /// 获取当前机器的唯一标识码
        /// </summary>
        /// <returns>机器码</returns>
        public static string GetMachineCode()
        {
            // 收集硬件信息
            StringBuilder sb = new StringBuilder();
            
            // 获取CPU ID
            string cpuId = GetCpuId();
            if (!string.IsNullOrEmpty(cpuId))
                sb.Append(cpuId);
            
            // 获取主板序列号
            string boardId = GetMotherboardId();
            if (!string.IsNullOrEmpty(boardId))
                sb.Append(boardId);
            
            // 如果无法获取硬件信息，使用机器名和系统盘序列号
            if (sb.Length == 0)
            {
                sb.Append(Environment.MachineName);
                string diskId = GetSystemDiskId();
                if (!string.IsNullOrEmpty(diskId))
                    sb.Append(diskId);
            }
            
            // 计算SHA-1哈希
            using (SHA1 sha1 = SHA1.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(sb.ToString());
                byte[] hashBytes = sha1.ComputeHash(inputBytes);
                
                // 将哈希转换为16进制字符串
                StringBuilder hashSb = new StringBuilder();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    hashSb.Append(hashBytes[i].ToString("X2"));
                }
                
                // 格式化为更易读的形式 (xxxx-xxxx-xxxx-xxxx-xxxx)
                string fullHash = hashSb.ToString();
                StringBuilder formattedHash = new StringBuilder();
                
                for (int i = 0; i < Math.Min(fullHash.Length, 20); i += 4)
                {
                    if (i + 4 <= fullHash.Length)
                    {
                        formattedHash.Append(fullHash.Substring(i, 4));
                        if (i + 4 < 20) formattedHash.Append("-");
                    }
                }
                
                return formattedHash.ToString();
            }
        }
        
        /// <summary>
        /// 获取CPU ID
        /// </summary>
        private static string GetCpuId()
        {
            try
            {
                ManagementClass mc = new ManagementClass("Win32_Processor");
                ManagementObjectCollection moc = mc.GetInstances();
                
                foreach (ManagementObject mo in moc)
                {
                    return mo["ProcessorId"].ToString();
                }
            }
            catch
            {
                // 忽略异常
            }
            
            return string.Empty;
        }
        
        /// <summary>
        /// 获取主板ID
        /// </summary>
        private static string GetMotherboardId()
        {
            try
            {
                ManagementClass mc = new ManagementClass("Win32_BaseBoard");
                ManagementObjectCollection moc = mc.GetInstances();
                
                foreach (ManagementObject mo in moc)
                {
                    return mo["SerialNumber"].ToString();
                }
            }
            catch
            {
                // 忽略异常
            }
            
            return string.Empty;
        }
        
        /// <summary>
        /// 获取系统磁盘ID
        /// </summary>
        private static string GetSystemDiskId()
        {
            try
            {
                string systemDriveLetter = Environment.GetFolderPath(Environment.SpecialFolder.System).Substring(0, 1);
                
                ManagementObject diskDrive = new ManagementObject(
                    "Win32_LogicalDisk.DeviceID=\"" + systemDriveLetter + ":\"");
                diskDrive.Get();
                
                return diskDrive["VolumeSerialNumber"].ToString();
            }
            catch
            {
                // 忽略异常
            }
            
            return string.Empty;
        }
    }
} 