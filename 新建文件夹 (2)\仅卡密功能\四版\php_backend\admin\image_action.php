<?php
session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// 包含数据库配置
require_once '../config.php';

// 处理镜像上传
function handleImageUpload() {
    global $conn;
    
    // 获取表单数据
    $name = $_POST['name'] ?? '';
    $version = $_POST['version'] ?? '';
    $description = $_POST['description'] ?? '';
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $upload_type = $_POST['upload_type'] ?? 'file';
    
    // 检查必填字段
    if (empty($name) || empty($version)) {
        header('Location: images.php?error=镜像名称和版本不能为空');
        exit;
    }
    
    // 生成唯一文件标识
    $file_id = uniqid();
    
    // 根据上传类型处理
    if ($upload_type === 'file') {
        // 本地文件上传方式
        
        // 检查是否有上传错误
        if (!isset($_FILES['image_file']) || $_FILES['image_file']['error'] > 0) {
            header('Location: images.php?error=文件上传失败: ' . ($_FILES['image_file']['error'] ?? '未选择文件'));
            exit;
        }
        
        // 获取文件信息
        $file_name = $_FILES['image_file']['name'];
        $file_size = $_FILES['image_file']['size'];
        $file_tmp = $_FILES['image_file']['tmp_name'];
        $file_type = $_FILES['image_file']['type'];
        $file_hash = hash_file('sha256', $file_tmp);
        
        // 创建存储目录
        $upload_dir = __DIR__ . '/../uploads/images/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        // 生成唯一文件名
        $new_file_name = $file_id . '_' . $file_name;
        $file_path = $upload_dir . $new_file_name;
        
        // 保存文件
        if (!move_uploaded_file($file_tmp, $file_path)) {
            header('Location: images.php?error=保存文件失败');
            exit;
        }
        
        // 文件网络路径为本地路径
        $is_remote = 0;
        $file_url = $new_file_name;
        
    } else if ($upload_type === 'url') {
        // URL直链方式
        
        $file_url = $_POST['image_url'] ?? '';
        if (empty($file_url)) {
            header('Location: images.php?error=请提供有效的镜像URL');
            exit;
        }
        
        // 验证URL格式
        if (!filter_var($file_url, FILTER_VALIDATE_URL)) {
            header('Location: images.php?error=无效的URL格式');
            exit;
        }
        
        // 从URL提取文件名
        $file_name = basename(parse_url($file_url, PHP_URL_PATH));
        if (empty($file_name)) {
            $file_name = 'remote_image_' . $file_id . '.iso';
        }
        
        // 获取用户提供的文件大小（MB）并转换为字节
        $file_size_mb = $_POST['file_size'] ?? 0;
        $file_size = $file_size_mb * 1024 * 1024; // 转换为字节
        
        // 对于远程URL，我们不计算哈希值
        $file_hash = '';
        
        // 标记为远程文件
        $is_remote = 1;
        
    } else {
        header('Location: images.php?error=无效的上传类型');
        exit;
    }
    
    // 保存到数据库
    $stmt = mysqli_prepare($conn, "INSERT INTO system_images (name, version, description, file_path, file_size, file_hash, is_active, is_remote) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    
    if ($stmt === false) {
        header('Location: images.php?error=保存镜像信息失败: ' . mysqli_error($conn));
        exit;
    }
    
    mysqli_stmt_bind_param($stmt, "ssssdsis", $name, $version, $description, $file_url, $file_size, $file_hash, $is_active, $is_remote);
    
    if (mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        header('Location: images.php?success=系统镜像添加成功');
        exit;
    } else {
        mysqli_stmt_close($stmt);
        header('Location: images.php?error=保存镜像信息失败: ' . mysqli_error($conn));
        exit;
    }
}

// 处理镜像状态切换
function handleToggleStatus() {
    global $conn;
    
    if (!isset($_POST['image_id']) || !isset($_POST['is_active'])) {
        echo json_encode([
            'status' => 'error',
            'message' => '参数错误'
        ]);
        exit;
    }
    
    $imageId = $_POST['image_id'];
    $isActive = (int)$_POST['is_active'];
    
    $stmt = mysqli_prepare($conn, "UPDATE system_images SET is_active = ? WHERE id = ?");
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => '更新镜像状态失败: ' . mysqli_error($conn)
        ]);
        exit;
    }
    
    mysqli_stmt_bind_param($stmt, "ii", $isActive, $imageId);
    
    if (mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'success',
            'message' => '镜像状态已更新'
        ]);
    } else {
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'status' => 'error',
            'message' => '更新镜像状态失败: ' . mysqli_error($conn)
        ]);
    }
}

// 处理镜像删除
function handleDeleteImage() {
    global $conn;
    
    if (!isset($_POST['image_id'])) {
        header('Location: images.php?error=参数错误');
        exit;
    }
    
    $imageId = $_POST['image_id'];
    
    // 首先获取镜像信息
    $stmt = mysqli_prepare($conn, "SELECT file_path, is_remote FROM system_images WHERE id = ?");
    
    if ($stmt === false) {
        header('Location: images.php?error=查询系统镜像失败: ' . mysqli_error($conn));
        exit;
    }
    
    mysqli_stmt_bind_param($stmt, "i", $imageId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($image = mysqli_fetch_assoc($result)) {
        mysqli_stmt_close($stmt);
        
        // 如果是本地文件，才删除文件
        if ($image['is_remote'] == 0) {
            // 删除本地文件
            $file_path = __DIR__ . '/../uploads/images/' . $image['file_path'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }
        
        // 删除数据库记录
        $stmt = mysqli_prepare($conn, "DELETE FROM system_images WHERE id = ?");
        
        if ($stmt === false) {
            header('Location: images.php?error=删除镜像记录失败: ' . mysqli_error($conn));
            exit;
        }
        
        mysqli_stmt_bind_param($stmt, "i", $imageId);
        
        if (mysqli_stmt_execute($stmt)) {
            mysqli_stmt_close($stmt);
            header('Location: images.php?success=系统镜像已删除');
            exit;
        } else {
            mysqli_stmt_close($stmt);
            header('Location: images.php?error=删除镜像记录失败: ' . mysqli_error($conn));
            exit;
        }
    } else {
        mysqli_stmt_close($stmt);
        header('Location: images.php?error=找不到指定的系统镜像');
        exit;
    }
}

// 路由处理
$action = $_REQUEST['action'] ?? '';

header('Content-Type: application/json');

switch ($action) {
    case 'upload':
        header('Content-Type: text/html; charset=utf-8');
        handleImageUpload();
        break;
    
    case 'toggle_status':
        handleToggleStatus();
        break;
    
    case 'delete':
        header('Content-Type: text/html; charset=utf-8');
        handleDeleteImage();
        break;
    
    default:
        echo json_encode([
            'status' => 'error',
            'message' => '未知的操作: ' . $action
        ]);
} 