using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace SystemReinstaller.Utils
{
    /// <summary>
    /// API调用工具类
    /// </summary>
    public static class ApiUtils
    {
        private static readonly HttpClient httpClient = new HttpClient();
        private static readonly string apiUrl = "http://154.12.39.200/api.php";
        
        /// <summary>
        /// API基础URL，用于构建完整的API地址
        /// </summary>
        public static string ApiBaseUrl => "http://154.12.39.200/";
        
        /// <summary>
        /// 验证卡密
        /// </summary>
        /// <param name="cardKey">卡密</param>
        /// <returns>验证结果</returns>
        public static async Task<(bool Success, string Message, string ExpireDate, int UsesLeft)> VerifyCardKey(string cardKey)
        {
            try
            {
                var formData = new Dictionary<string, string>
                {
                    { "action", "verify_key" },
                    { "card_key", cardKey }
                };
                
                var response = await PostFormAsync(formData);
                
                if (response.TryGetProperty("status", out JsonElement statusElement) && 
                    statusElement.GetString() == "success")
                {
                    string expireDate = response.GetProperty("expire_date").GetString();
                    int usesLeft = response.GetProperty("uses_left").GetInt32();
                    
                    return (true, "验证成功", expireDate, usesLeft);
                }
                else
                {
                    string message = response.TryGetProperty("message", out JsonElement messageElement) ? 
                        messageElement.GetString() : "验证失败";
                    
                    return (false, message, string.Empty, 0);
                }
            }
            catch (Exception ex)
            {
                return (false, $"请求异常: {ex.Message}", string.Empty, 0);
            }
        }
        
        /// <summary>
        /// 绑定机器码
        /// </summary>
        /// <param name="cardKey">卡密</param>
        /// <param name="machineCode">机器码</param>
        /// <returns>绑定结果</returns>
        public static async Task<(bool Success, string Message)> BindMachine(string cardKey, string machineCode)
        {
            try
            {
                var formData = new Dictionary<string, string>
                {
                    { "action", "bind_machine" },
                    { "card_key", cardKey },
                    { "machine_code", machineCode }
                };
                
                var response = await PostFormAsync(formData);
                
                if (response.TryGetProperty("status", out JsonElement statusElement) && 
                    statusElement.GetString() == "success")
                {
                    string message = response.TryGetProperty("message", out JsonElement messageElement) ? 
                        messageElement.GetString() : "绑定成功";
                    
                    return (true, message);
                }
                else
                {
                    string message = response.TryGetProperty("message", out JsonElement messageElement) ? 
                        messageElement.GetString() : "绑定失败";
                    
                    return (false, message);
                }
            }
            catch (Exception ex)
            {
                return (false, $"请求异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 解绑机器码
        /// </summary>
        /// <param name="cardKey">卡密</param>
        /// <param name="machineCode">机器码</param>
        /// <returns>解绑结果</returns>
        public static async Task<(bool Success, string Message)> UnbindMachine(string cardKey, string machineCode)
        {
            try
            {
                var formData = new Dictionary<string, string>
                {
                    { "action", "unbind_machine" },
                    { "card_key", cardKey },
                    { "machine_code", machineCode }
                };
                
                var response = await PostFormAsync(formData);
                
                if (response.TryGetProperty("status", out JsonElement statusElement) && 
                    statusElement.GetString() == "success")
                {
                    string message = response.TryGetProperty("message", out JsonElement messageElement) ? 
                        messageElement.GetString() : "解绑成功";
                    
                    return (true, message);
                }
                else
                {
                    string message = response.TryGetProperty("message", out JsonElement messageElement) ? 
                        messageElement.GetString() : "解绑失败";
                    
                    return (false, message);
                }
            }
            catch (Exception ex)
            {
                return (false, $"请求异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 检查机器是否已绑定
        /// </summary>
        /// <param name="cardKey">卡密</param>
        /// <param name="machineCode">机器码</param>
        /// <returns>绑定状态</returns>
        public static async Task<(bool Success, bool IsBound)> CheckMachineBound(string cardKey, string machineCode)
        {
            try
            {
                var formData = new Dictionary<string, string>
                {
                    { "action", "check_machine" },
                    { "card_key", cardKey },
                    { "machine_code", machineCode }
                };
                
                var response = await PostFormAsync(formData);
                
                if (response.TryGetProperty("status", out JsonElement statusElement) && 
                    statusElement.GetString() == "success")
                {
                    bool isBound = response.TryGetProperty("is_bound", out JsonElement boundElement) && 
                        boundElement.GetBoolean();
                    
                    return (true, isBound);
                }
                else
                {
                    return (false, false);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查机器绑定状态异常: {ex.Message}");
                return (false, false);
            }
        }
        
        /// <summary>
        /// 获取系统镜像列表
        /// </summary>
        /// <returns>系统镜像列表</returns>
        public static async Task<(bool Success, List<SystemImage> Images, string Message)> GetSystemImages()
        {
            try
            {
                var formData = new Dictionary<string, string>
                {
                    { "action", "get_system_images" }
                };
                
                var response = await PostFormAsync(formData);
                
                if (response.TryGetProperty("status", out JsonElement statusElement) && 
                    statusElement.GetString() == "success")
                {
                    var images = new List<SystemImage>();
                    
                    if (response.TryGetProperty("images", out JsonElement imagesElement) && 
                        imagesElement.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var imageElement in imagesElement.EnumerateArray())
                        {
                            var image = new SystemImage
                            {
                                Id = imageElement.TryGetProperty("id", out JsonElement idElement) ? 
                                    idElement.GetInt32() : 0,
                                Name = imageElement.TryGetProperty("name", out JsonElement nameElement) ? 
                                    nameElement.GetString() : string.Empty,
                                Description = imageElement.TryGetProperty("description", out JsonElement descElement) ? 
                                    descElement.GetString() : string.Empty,
                                Size = imageElement.TryGetProperty("size", out JsonElement sizeElement) ? 
                                    sizeElement.GetString() : string.Empty,
                                DownloadUrl = imageElement.TryGetProperty("download_url", out JsonElement urlElement) ? 
                                    urlElement.GetString() : string.Empty
                            };
                            
                            images.Add(image);
                        }
                    }
                    
                    return (true, images, "获取成功");
                }
                else
                {
                    string message = response.TryGetProperty("message", out JsonElement messageElement) ? 
                        messageElement.GetString() : "获取系统镜像列表失败";
                    
                    return (false, new List<SystemImage>(), message);
                }
            }
            catch (Exception ex)
            {
                return (false, new List<SystemImage>(), $"请求异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 请求下载系统镜像
        /// </summary>
        /// <param name="imageId">镜像ID</param>
        /// <param name="cardKey">卡密</param>
        /// <param name="machineCode">机器码</param>
        /// <returns>下载请求结果</returns>
        public static async Task<(bool Success, string DownloadUrl, string FileName, long FileSize, string Message)> RequestDownloadImage(int imageId, string cardKey, string machineCode)
        {
            try
            {
                var formData = new Dictionary<string, string>
                {
                    { "action", "request_download" },
                    { "image_id", imageId.ToString() },
                    { "card_key", cardKey },
                    { "machine_code", machineCode }
                };
                
                var response = await PostFormAsync(formData);
                
                if (response.TryGetProperty("status", out JsonElement statusElement) && 
                    statusElement.GetString() == "success")
                {
                    string downloadUrl = response.TryGetProperty("download_url", out JsonElement urlElement) ? 
                        urlElement.GetString() : string.Empty;
                    
                    string fileName = response.TryGetProperty("file_name", out JsonElement fileNameElement) ? 
                        fileNameElement.GetString() : $"system_image_{imageId}";
                    
                    long fileSize = response.TryGetProperty("file_size", out JsonElement fileSizeElement) ? 
                        fileSizeElement.GetInt64() : 0;
                    
                    return (true, downloadUrl, fileName, fileSize, "请求下载成功");
                }
                else
                {
                    string message = response.TryGetProperty("message", out JsonElement messageElement) ? 
                        messageElement.GetString() : "请求下载失败";
                    
                    return (false, string.Empty, string.Empty, 0, message);
                }
            }
            catch (Exception ex)
            {
                return (false, string.Empty, string.Empty, 0, $"请求异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 发送POST请求
        /// </summary>
        private static async Task<JsonElement> PostFormAsync(Dictionary<string, string> formData)
        {
            using var content = new FormUrlEncodedContent(formData);
            using var response = await httpClient.PostAsync(apiUrl, content);
            
            response.EnsureSuccessStatusCode();
            string responseBody = await response.Content.ReadAsStringAsync();
            
            try
            {
                using JsonDocument document = JsonDocument.Parse(responseBody);
                return document.RootElement.Clone();
            }
            catch
            {
                // 如果解析失败，返回错误信息
                var errorJson = $"{{\"status\":\"error\",\"message\":\"无法解析响应: {responseBody}\"}}";
                using JsonDocument document = JsonDocument.Parse(errorJson);
                return document.RootElement.Clone();
            }
        }
    }
    
    /// <summary>
    /// 系统镜像信息类
    /// </summary>
    public class SystemImage
    {
        /// <summary>
        /// 镜像ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 镜像名称
        /// </summary>
        public required string Name { get; set; }
        
        /// <summary>
        /// 镜像描述
        /// </summary>
        public required string Description { get; set; }
        
        /// <summary>
        /// 镜像大小
        /// </summary>
        public required string Size { get; set; }
        
        /// <summary>
        /// 下载地址
        /// </summary>
        public required string DownloadUrl { get; set; }
        
        /// <summary>
        /// 重写ToString方法，用于在ComboBox中显示
        /// </summary>
        public override string ToString()
        {
            return Name;
        }
    }
} 