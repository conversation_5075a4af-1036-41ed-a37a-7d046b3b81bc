<?php
session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// 包含数据库配置
require_once '../config.php';

// 获取所有系统镜像
$stmt = mysqli_prepare($conn, "SELECT * FROM system_images ORDER BY created_at DESC");

if (!$stmt) {
    die("查询系统镜像失败: " . mysqli_error($conn));
}

mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$images = [];

while ($row = mysqli_fetch_assoc($result)) {
    $images[] = $row;
}

mysqli_stmt_close($stmt);
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统镜像管理 - 管理员后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <?php include 'navbar.php'; ?>
    
    <div class="container my-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>系统镜像管理</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                <i class="bi bi-cloud-upload"></i> 上传新镜像
            </button>
        </div>
        
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_GET['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_GET['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>镜像名称</th>
                                <th>系统版本</th>
                                <th>文件大小</th>
                                <th>来源</th>
                                <th>下载次数</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($images)): ?>
                                <tr>
                                    <td colspan="9" class="text-center">暂无系统镜像</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($images as $image): ?>
                                    <tr>
                                        <td><?php echo $image['id']; ?></td>
                                        <td><?php echo htmlspecialchars($image['name']); ?></td>
                                        <td><?php echo htmlspecialchars($image['version']); ?></td>
                                        <td><?php echo formatFileSize($image['file_size']); ?></td>
                                        <td>
                                            <?php if ($image['is_remote'] == 1): ?>
                                                <span class="badge bg-info">远程直链</span>
                                                <a href="#" class="text-info" data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($image['file_path']); ?>">
                                                    <i class="bi bi-info-circle"></i>
                                                </a>
                                            <?php else: ?>
                                                <span class="badge bg-success">本地文件</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $image['download_count']; ?></td>
                                        <td>
                                            <?php if ($image['is_active']): ?>
                                                <span class="badge bg-success toggle-status" data-id="<?php echo $image['id']; ?>" data-status="1">已激活</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary toggle-status" data-id="<?php echo $image['id']; ?>" data-status="0">未激活</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo isset($image['created_at']) ? $image['created_at'] : '未设置'; ?></td>
                                        <td>
                                            <form method="post" action="image_action.php" class="d-inline" onsubmit="return confirm('确定要删除该系统镜像吗？');">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="image_id" value="<?php echo $image['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger">删除</button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 上传镜像模态框 -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">上传系统镜像</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="image_action.php" method="post" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="upload">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">镜像名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="version" class="form-label">系统版本 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="version" name="version" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">镜像描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">添加方式 <span class="text-danger">*</span></label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="upload_type" id="upload_type_file" value="file" checked>
                                <label class="form-check-label" for="upload_type_file">
                                    本地上传
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="upload_type" id="upload_type_url" value="url">
                                <label class="form-check-label" for="upload_type_url">
                                    直链地址
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="file_upload_section">
                            <label for="image_file" class="form-label">选择文件 <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="image_file" name="image_file">
                            <div class="form-text">支持ISO文件格式，最大上传大小：2GB</div>
                        </div>
                        
                        <div class="mb-3" id="url_upload_section" style="display:none;">
                            <label for="image_url" class="form-label">直链地址 <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" id="image_url" name="image_url" placeholder="例如：http://example.com/Windows.ISO">
                            <div class="form-text">请输入直接指向ISO文件的完整URL地址</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="file_size" class="form-label">文件大小(MB)</label>
                            <input type="number" class="form-control" id="file_size" name="file_size" placeholder="选填，直链方式需填写">
                            <div class="form-text">直链方式需要手动填写文件大小（单位：MB）</div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">立即激活</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">上传</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 查看镜像详情模态框 -->
    <div class="modal fade" id="viewModal" tabindex="-1" aria-labelledby="viewModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewModalLabel">镜像详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="imageDetails" class="p-2">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除镜像 "<span id="imageNameToDelete"></span>" 吗？</p>
                    <p class="text-danger">此操作不可逆，将同时删除镜像文件和数据库记录！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <form id="deleteForm" action="image_action.php" method="post">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="image_id" id="imageIdToDelete">
                        <button type="submit" class="btn btn-danger">确认删除</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // 查看镜像详情
            $('.view-btn').click(function() {
                const imageId = $(this).data('id');
                $('#imageDetails').html(`
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">加载中...</p>
                    </div>
                `);
                $('#viewModal').modal('show');
                
                // 使用AJAX获取镜像详情
                $.get('../api.php', { 
                    action: 'getSystemImageDetail', 
                    image_id: imageId 
                }, function(response) {
                    if (response.status === 'success') {
                        const image = response.data;
                        $('#imageDetails').html(`
                            <div class="mb-3">
                                <strong>ID:</strong> ${image.id}
                            </div>
                            <div class="mb-3">
                                <strong>镜像名称:</strong> ${image.name}
                            </div>
                            <div class="mb-3">
                                <strong>系统版本:</strong> ${image.version}
                            </div>
                            <div class="mb-3">
                                <strong>镜像描述:</strong> <p>${image.description || '无'}</p>
                            </div>
                            <div class="mb-3">
                                <strong>文件大小:</strong> ${formatFileSize(image.file_size)}
                            </div>
                            <div class="mb-3">
                                <strong>文件路径:</strong> ${image.file_path}
                            </div>
                            <div class="mb-3">
                                <strong>文件哈希:</strong> ${image.file_hash || '未计算'}
                            </div>
                            <div class="mb-3">
                                <strong>下载次数:</strong> ${image.download_count}
                            </div>
                            <div class="mb-3">
                                <strong>状态:</strong> ${image.is_active == 1 ? '<span class="badge bg-success">激活</span>' : '<span class="badge bg-danger">禁用</span>'}
                            </div>
                            <div class="mb-3">
                                <strong>创建时间:</strong> ${new Date(image.created_at).toLocaleString()}
                            </div>
                            <div class="mb-3">
                                <strong>更新时间:</strong> ${new Date(image.updated_at).toLocaleString()}
                            </div>
                        `);
                    } else {
                        $('#imageDetails').html(`<div class="alert alert-danger">${response.message}</div>`);
                    }
                }, 'json').fail(function() {
                    $('#imageDetails').html(`<div class="alert alert-danger">加载镜像详情失败，请稍后再试</div>`);
                });
            });
            
            // 更改镜像状态
            $('.toggle-status-btn').click(function() {
                const imageId = $(this).data('id');
                const newStatus = $(this).data('status');
                
                $.post('image_action.php', { 
                    action: 'toggle_status', 
                    image_id: imageId,
                    is_active: newStatus
                }, function(response) {
                    if (response.status === 'success') {
                        location.reload();
                    } else {
                        alert('更改状态失败: ' + response.message);
                    }
                }, 'json').fail(function() {
                    alert('更改状态失败，请稍后再试');
                });
            });
            
            // 删除镜像
            $('.delete-btn').click(function() {
                const imageId = $(this).data('id');
                const imageName = $(this).data('name');
                
                $('#imageIdToDelete').val(imageId);
                $('#imageNameToDelete').text(imageName);
                $('#deleteModal').modal('show');
            });
        });
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 初始化tooltips
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
        
        // 处理上传类型切换
        document.addEventListener('DOMContentLoaded', function() {
            const fileRadio = document.getElementById('upload_type_file');
            const urlRadio = document.getElementById('upload_type_url');
            const fileSection = document.getElementById('file_upload_section');
            const urlSection = document.getElementById('url_upload_section');
            const fileInput = document.getElementById('image_file');
            const urlInput = document.getElementById('image_url');
            
            fileRadio.addEventListener('change', function() {
                if(this.checked) {
                    fileSection.style.display = 'block';
                    urlSection.style.display = 'none';
                    fileInput.setAttribute('required', 'required');
                    urlInput.removeAttribute('required');
                }
            });
            
            urlRadio.addEventListener('change', function() {
                if(this.checked) {
                    fileSection.style.display = 'none';
                    urlSection.style.display = 'block';
                    urlInput.setAttribute('required', 'required');
                    fileInput.removeAttribute('required');
                }
            });
        });
    </script>
</body>
</html>

<?php
// 格式化文件大小
function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    $i = floor(log($bytes) / log($k));
    
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?> 